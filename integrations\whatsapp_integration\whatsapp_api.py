"""
WhatsApp Messaging Integration using Unipile API
Implements the preferred WhatsAppMessaging class structure
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unipile_config import UnipileClient
import json
import qrcode
import io
import base64
from typing import List, Dict, Any, Optional
import time
import requests


class WhatsAppMessaging:
    """WhatsApp messaging class with Unipile API integration"""
    
    def __init__(self, unipile_api_key: str = None):
        """Initialize WhatsApp messaging with Unipile API key"""
        if unipile_api_key is None:
            # Use the working API key
            unipile_api_key = "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="
        
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.is_authenticated = False
        self.qr_code_data = None
        
        # Load configuration
        self.config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        self.load_config()
    
    def load_config(self):
        """Load WhatsApp configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            self.config = {
                "account_id": None,
                "phone_number": None,
                "last_authenticated": None
            }
            self.save_config()
    
    def save_config(self):
        """Save WhatsApp configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def authenticate_account(self) -> Dict[str, Any]:
        """
        Generate QR code for WhatsApp Web authentication
        Returns authentication data including QR code
        """
        try:
            # Request WhatsApp authentication
            auth_response = self.unipile.authenticate_account("whatsapp")

            # Debug: Print the full response for troubleshooting
            print(f"DEBUG: Unipile auth response: {auth_response}")

            if auth_response.get("success"):
                # Generate QR code if provided
                if "qr_code" in auth_response:
                    self.qr_code_data = auth_response["qr_code"]
                    qr_image = self.generate_qr_code(self.qr_code_data)
                    auth_response["qr_image"] = qr_image

                # Store account information
                if "account_id" in auth_response:
                    self.account_id = auth_response["account_id"]
                    self.config["account_id"] = self.account_id
                    self.config["last_qr_generated"] = time.strftime("%Y-%m-%d %H:%M:%S")
                    self.save_config()
                    print(f"DEBUG: Account ID stored: {self.account_id}")

                return {
                    "success": True,
                    "message": "QR code generated. Please scan with WhatsApp",
                    "qr_code": auth_response.get("qr_code"),
                    "qr_image": auth_response.get("qr_image"),
                    "account_id": self.account_id
                }
            else:
                # Provide detailed error information
                error_details = {
                    "success": False,
                    "message": "Failed to initiate WhatsApp authentication",
                    "error": auth_response.get("error", "Unknown error"),
                    "status_code": auth_response.get("status_code"),
                    "response_text": auth_response.get("response_text"),
                    "full_response": auth_response
                }
                print(f"DEBUG: Authentication failed with details: {error_details}")
                return error_details

        except Exception as e:
            error_details = {
                "success": False,
                "message": f"Authentication error: {str(e)}",
                "exception_type": type(e).__name__
            }
            print(f"DEBUG: Exception during authentication: {error_details}")
            return error_details
    
    def generate_qr_code(self, data: str) -> str:
        """Generate QR code image as base64 string"""
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/png;base64,{img_str}"
    
    def check_authentication_status(self) -> Dict[str, Any]:
        """Check if WhatsApp account is authenticated and connected"""
        # Try to load account ID from config if not set
        if not self.account_id and self.config.get("account_id"):
            self.account_id = self.config["account_id"]
            print(f"DEBUG: Loaded account ID from config: {self.account_id}")

        if not self.account_id:
            return {
                "authenticated": False,
                "message": "No account ID found. Please generate QR code first.",
                "status": "NO_ACCOUNT"
            }

        try:
            status = self.unipile.get_account_status(self.account_id)
            self.is_authenticated = status.get("connected", False)

            # Determine user-friendly status message
            if self.is_authenticated:
                # Update config with successful authentication
                self.config["last_authenticated"] = time.strftime("%Y-%m-%d %H:%M:%S")
                self.config["phone_number"] = status.get("phone_number")
                self.save_config()

                return {
                    "authenticated": True,
                    "account_id": self.account_id,
                    "status": status.get("status", "CONNECTED"),
                    "phone_number": status.get("phone_number"),
                    "display_name": status.get("display_name"),
                    "connection_time": status.get("connection_time"),
                    "message": "WhatsApp account connected successfully!",
                    "success_notification": True
                }
            else:
                # Account exists but not connected yet
                account_status = status.get("status", "PENDING")
                if account_status == "PENDING":
                    message = "Waiting for QR code scan. Please scan the QR code with WhatsApp."
                elif account_status == "DISCONNECTED":
                    message = "WhatsApp account was disconnected. Please scan QR code again."
                else:
                    message = f"Account status: {account_status}. Please check your WhatsApp connection."

                return {
                    "authenticated": False,
                    "account_id": self.account_id,
                    "status": account_status,
                    "phone_number": status.get("phone_number"),
                    "message": message,
                    "waiting_for_scan": account_status == "PENDING"
                }

        except Exception as e:
            return {
                "authenticated": False,
                "message": f"Status check failed: {str(e)}",
                "status": "ERROR",
                "error": str(e)
            }
    
    def send_message(self, phone_number: str, message: str) -> Dict[str, Any]:
        """
        Send message to a specific phone number
        Args:
            phone_number: Target phone number (with country code)
            message: Message text to send
        """
        if not self.is_authenticated and not self.check_authentication_status()["authenticated"]:
            return {
                "success": False,
                "message": "WhatsApp account not authenticated"
            }
        
        try:
            # Format phone number if needed
            formatted_number = self.format_phone_number(phone_number)
            
            # Send message using Unipile API with preferred format
            response = self.unipile.send_message(
                account_id=self.account_id,
                recipient=formatted_number,
                message=message
            )
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": "Message sent successfully",
                    "message_id": response.get("message_id"),
                    "recipient": formatted_number
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send message",
                    "error": response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Send message error: {str(e)}"
            }
    
    def send_bulk_messages(self, recipients: List[Dict[str, str]], message: str) -> Dict[str, Any]:
        """
        Send bulk messages to multiple recipients
        Args:
            recipients: List of recipient data [{"phone": "+**********", "name": "John"}]
            message: Message text to send to all recipients
        """
        if not self.is_authenticated and not self.check_authentication_status()["authenticated"]:
            return {
                "success": False,
                "message": "WhatsApp account not authenticated"
            }
        
        results = []
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            phone_number = recipient.get("phone") or recipient.get("phone_number")
            if not phone_number:
                results.append({
                    "recipient": recipient,
                    "success": False,
                    "message": "No phone number provided"
                })
                failed_sends += 1
                continue
            
            # Personalize message if name is provided
            personalized_message = message
            if recipient.get("name"):
                personalized_message = f"Hi {recipient['name']}, {message}"
            
            # Send individual message
            send_result = self.send_message(phone_number, personalized_message)
            results.append({
                "recipient": recipient,
                "success": send_result["success"],
                "message": send_result["message"],
                "message_id": send_result.get("message_id")
            })
            
            if send_result["success"]:
                successful_sends += 1
            else:
                failed_sends += 1
            
            # Add delay between messages to avoid rate limiting
            time.sleep(1)
        
        return {
            "success": True,
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "results": results
        }
    
    def format_phone_number(self, phone_number: str) -> str:
        """Format phone number for WhatsApp API"""
        # Remove any non-digit characters except +
        cleaned = ''.join(c for c in phone_number if c.isdigit() or c == '+')
        
        # Ensure it starts with +
        if not cleaned.startswith('+'):
            cleaned = '+' + cleaned
        
        return cleaned
    
    def get_chat_history(self, phone_number: str, limit: int = 50) -> Dict[str, Any]:
        """Get chat history with a specific contact"""
        try:
            formatted_number = self.format_phone_number(phone_number)
            # This would need to be implemented based on Unipile's chat history API
            # For now, return a placeholder
            return {
                "success": True,
                "chat_history": [],
                "message": "Chat history feature to be implemented"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to get chat history: {str(e)}"
            }
