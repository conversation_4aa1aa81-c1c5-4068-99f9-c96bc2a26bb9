# Social Media Messaging Integrations

A comprehensive messaging system for all major social media platforms, designed to send messages to scraped leads across WhatsApp, Telegram, Facebook, Instagram, TikTok, and LinkedIn.

## 🚀 Features

- **Unified Messaging System**: Single API to send messages across all platforms
- **Platform-Specific Integrations**: Dedicated modules for each social media platform
- **Unipile Integration**: Primary method using Unipile API with platform-specific fallbacks
- **Web Authentication**: HTML interfaces for easy account setup and authentication
- **Bulk Messaging**: Send messages to multiple recipients across platforms
- **Real-time Status**: Check connection status and authentication state
- **Rate Limiting**: Built-in delays to respect platform rate limits

## 📁 Project Structure

```
integrations/
├── whatsapp_integration/
│   ├── whatsapp_auth.html
│   ├── whatsapp_api.py
│   ├── config.json
│   └── __init__.py
├── telegram_integration/
│   ├── telegram_auth.html
│   ├── telegram_api.py
│   ├── config.json
│   └── __init__.py
├── facebook_integration/
│   ├── facebook_auth.html
│   ├── facebook_api.py
│   ├── config.json
│   └── __init__.py
├── instagram_integration/
│   ├── instagram_auth.html
│   ├── instagram_api.py
│   ├── config.json
│   └── __init__.py
├── tiktok_integration/
│   ├── tiktok_auth.html
│   ├── tiktok_api.py
│   ├── config.json
│   └── __init__.py
├── linkedin_integration/
│   ├── linkedin_auth.html
│   ├── linkedin_api.py
│   ├── config.json
│   └── __init__.py
├── unipile_config.py
└── unified_messaging.py
api_server.py
requirements.txt
README.md
```

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd social-media-messaging
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Start the API server**:
   ```bash
   python api_server.py
   ```

4. **Access the system**:
   - API Documentation: http://localhost:5000/
   - Authentication Pages: http://localhost:5000/auth/<platform>

## 🔧 Configuration

The system uses the Unipile API key: `b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s=`

Each platform has its own configuration file in `integrations/<platform>_integration/config.json`.

## 📱 Platform Setup

### WhatsApp
- **Method**: Unipile API + QR Code Authentication
- **Features**: Direct messaging, bulk messaging, media support
- **Setup**: Visit `/auth/whatsapp` and scan QR code with WhatsApp

### Telegram
- **Method**: Bot API + Unipile
- **Features**: Direct messaging, group messaging, bot interactions
- **Setup**: Create bot with @BotFather, enter token at `/auth/telegram`

### Facebook
- **Method**: Graph API + Unipile
- **Features**: Page messaging, Messenger API
- **Setup**: OAuth flow or direct access token at `/auth/facebook`

### Instagram
- **Method**: Business API + Unipile
- **Features**: Comment replies, limited direct messaging
- **Setup**: Business account required, setup at `/auth/instagram`

### TikTok
- **Method**: Business API + Unipile
- **Features**: Comment interactions, limited messaging
- **Setup**: Business API approval required, setup at `/auth/tiktok`

### LinkedIn
- **Method**: Marketing API + Unipile
- **Features**: Connection requests, InMail, professional messaging
- **Setup**: OAuth or access token at `/auth/linkedin`

## 🔌 API Endpoints

### Unified Messaging
- `POST /api/messaging/send` - Send single message
- `POST /api/messaging/bulk` - Send bulk messages
- `GET /api/messaging/status/<platform>` - Check platform status
- `GET /api/messaging/platforms` - Get supported platforms

### Platform-Specific
- `POST /api/<platform>/authenticate` - Authenticate platform
- `POST /api/<platform>/setup` - Setup platform credentials
- `GET /api/<platform>/test` - Test platform connection

## 📝 Usage Examples

### Send Single Message
```python
import requests

response = requests.post('http://localhost:5000/api/messaging/send', json={
    "platform": "whatsapp",
    "recipient": "+1234567890",
    "message": "Hello from our system!"
})
```

### Send Bulk Campaign
```python
campaign_data = {
    "whatsapp": [
        {"phone": "+1234567890", "message": "Hello John!", "name": "John"},
        {"phone": "+0987654321", "message": "Hi Jane!", "name": "Jane"}
    ],
    "telegram": [
        {"user_id": "@username", "message": "Hey Bob!", "name": "Bob"}
    ]
}

response = requests.post('http://localhost:5000/api/messaging/bulk', json={
    "campaign": campaign_data,
    "default_message": "Default message if not specified"
})
```

### Using Unified Messaging Class
```python
from integrations.unified_messaging import UnifiedMessaging

# Initialize
messaging = UnifiedMessaging()

# Send single message
result = messaging.send_message_by_platform(
    platform="whatsapp",
    recipient="+1234567890",
    message="Hello!"
)

# Send bulk campaign
campaign_result = messaging.send_bulk_campaign(campaign_data)

# Check platform status
status = messaging.get_platform_status("whatsapp")
```

## ⚠️ Platform Limitations

### WhatsApp
- Requires QR code authentication
- Rate limits apply
- Business API alternative available

### Telegram
- Users must start conversation with bot first
- Bot token required from @BotFather

### Facebook
- Requires Facebook page
- Messenger API permissions needed
- 24-hour messaging window for non-subscribers

### Instagram
- Very limited messaging capabilities
- Business account required
- Focus on comment replies

### TikTok
- Most restrictive platform
- Business API approval required
- Limited to comment interactions

### LinkedIn
- Strict professional policies
- Rate limits are very restrictive
- InMail requires premium features
- Connection required for messaging

## 🔒 Security Notes

- API keys are configured in the system
- Each platform has its own authentication method
- Tokens are stored locally in config files
- Use HTTPS in production
- Respect platform rate limits and policies

## 🚦 Rate Limits

Each platform has built-in rate limiting:
- WhatsApp: 1 second delay
- Telegram: 0.5 second delay
- Facebook: 1 second delay
- Instagram: 2 second delay
- TikTok: 2 second delay
- LinkedIn: 3 second delay

## 📊 Monitoring

The system includes:
- Logging to `logs/api_server.log`
- Real-time status checking
- Success/failure tracking
- Platform capability reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
1. Check the API documentation at http://localhost:5000/
2. Review platform-specific authentication pages
3. Check logs for error details
4. Ensure all dependencies are installed

## 🔄 Updates

The system is designed to be easily extensible. To add new platforms:
1. Create new integration module
2. Implement required methods
3. Add to unified messaging system
4. Create authentication HTML page
5. Add API endpoints

---

**Note**: This system is designed for legitimate business communication. Please respect all platform terms of service and anti-spam policies.
