<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Authentication - Social Media Messaging</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .qr-code-area {
            background: #f8f9fa;
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 40px 20px;
            margin: 30px 0;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .qr-code-placeholder {
            color: #999;
            font-size: 16px;
            text-align: center;
        }

        .qr-code-image {
            max-width: 250px;
            max-height: 250px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .status-indicator {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }

        .connect-btn, .refresh-btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .connect-btn:hover, .refresh-btn:hover {
            background: #128C7E;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        }

        .connect-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .refresh-btn {
            background: #007bff;
        }

        .refresh-btn:hover {
            background: #0056b3;
        }

        .account-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .account-info p {
            margin: 8px 0;
            color: #333;
        }

        .account-info strong {
            color: #25D366;
        }

        .instructions {
            background: #e9f7ef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .instructions h3 {
            color: #25D366;
            margin-bottom: 15px;
        }

        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .instructions li {
            margin: 8px 0;
            color: #333;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #25D366;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .account-info h4 {
            margin-top: 0;
            color: #333;
        }

        /* Success animations and notifications */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .success-animation {
            text-align: center;
            padding: 20px;
        }

        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #25D366;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1000;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease-in-out;
            max-width: 300px;
        }

        .success-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-content {
            text-align: center;
        }

        .notification-content h3 {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Enhanced status indicators */
        .status-indicator.status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            animation: fadeIn 0.5s ease-in-out;
        }

        .status-indicator.status-waiting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            animation: fadeIn 0.5s ease-in-out;
        }

        .status-indicator.status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            animation: fadeIn 0.5s ease-in-out;
        }

        /* Scanning indicator animation */
        .scanning-indicator {
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">📱</div>
        <h1>WhatsApp Authentication</h1>
        <p class="subtitle">Connect your WhatsApp account to start sending messages</p>

        <div class="qr-code-area" id="qr-code-area">
            <div class="qr-code-placeholder" id="qr-placeholder">
                Click "Generate QR Code" to start authentication
            </div>
        </div>

        <div class="status-indicator" id="status-indicator"></div>

        <div class="account-info" id="account-info" style="display: none;">
            <h4>Account Information</h4>
            <p><strong>Account ID:</strong> <span id="account-id">-</span></p>
            <p><strong>Phone Number:</strong> <span id="phone-number">-</span></p>
            <p><strong>Status:</strong> <span id="connection-status">-</span></p>
        </div>

        <button class="connect-btn" id="connect-btn" onclick="generateQRCode()">
            Generate QR Code
        </button>
        <button class="refresh-btn" id="refresh-btn" onclick="checkStatus()" style="display: none;">
            Check Status
        </button>

        <div class="instructions">
            <h3>How to Connect:</h3>
            <ol>
                <li>Click "Generate QR Code" button above</li>
                <li>Open WhatsApp on your phone</li>
                <li>Go to Settings → Linked Devices</li>
                <li>Tap "Link a Device"</li>
                <li>Scan the QR code displayed above</li>
                <li>Wait for confirmation</li>
            </ol>
        </div>
    </div>

    <script>
        let authCheckInterval;
        let currentAccountId = null;

        // API base URL - adjust as needed
        const API_BASE = '/api/whatsapp';

        async function generateQRCode() {
            const connectBtn = document.getElementById('connect-btn');
            const qrArea = document.getElementById('qr-code-area');
            const refreshBtn = document.getElementById('refresh-btn');

            // Show loading state
            connectBtn.disabled = true;
            connectBtn.textContent = 'Generating...';
            qrArea.innerHTML = '<div class="loading-spinner"></div><p style="margin-top: 15px;">Generating QR code...</p>';

            try {
                const response = await fetch(`${API_BASE}/authenticate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Display QR code
                    if (data.qr_image) {
                        qrArea.innerHTML = `
                            <img src="${data.qr_image}" alt="WhatsApp QR Code" class="qr-code-image">
                            <p style="margin-top: 15px; color: #666;">Scan this QR code with WhatsApp</p>
                            <div class="scanning-indicator" style="margin-top: 10px; color: #25D366; font-weight: 500;">
                                🔍 Waiting for scan... (Auto-detecting)
                            </div>
                        `;
                    }

                    // Store account ID
                    currentAccountId = data.account_id;
                    console.log('Account ID stored:', currentAccountId);

                    // Show refresh button
                    refreshBtn.style.display = 'inline-block';

                    // Start automatic monitoring immediately
                    startAuthCheck();

                } else {
                    showStatus('error', data.message || 'Failed to generate QR code');
                    qrArea.innerHTML = '<div class="qr-code-placeholder">Failed to generate QR code</div>';
                }

            } catch (error) {
                console.error('Error generating QR code:', error);
                showStatus('error', 'Network error. Please try again.');
                qrArea.innerHTML = '<div class="qr-code-placeholder">Network error occurred</div>';
            }

            // Reset button
            connectBtn.disabled = false;
            connectBtn.textContent = 'Regenerate QR Code';
        }

        async function checkStatus() {
            if (!currentAccountId) {
                console.log('No account ID available for status check');
                showStatus('error', 'No account ID available. Please generate QR code first.');
                return;
            }

            console.log('Checking status for account:', currentAccountId);

            try {
                const response = await fetch(`${API_BASE}/status`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Status check response:', data);

                if (data.authenticated) {
                    console.log('🎉 WhatsApp account authenticated successfully!');

                    // Stop monitoring
                    stopAuthCheck();

                    // Show success notification with animation
                    showStatus('success', data.message || 'WhatsApp account connected successfully!');
                    showAccountInfo(data);

                    // Update QR area with success animation
                    document.getElementById('qr-code-area').innerHTML = `
                        <div class="success-animation">
                            <div style="color: #25D366; font-size: 64px; animation: pulse 1.5s ease-in-out;">✓</div>
                            <p style="color: #25D366; font-weight: 600; margin-top: 15px; font-size: 18px;">Connected Successfully!</p>
                            <p style="color: #666; margin-top: 10px;">Your WhatsApp account is now linked and ready to use.</p>
                            <p style="color: #999; margin-top: 5px; font-size: 14px;">Phone: ${data.phone_number || 'Not available'}</p>
                        </div>
                    `;

                    // Hide refresh button and update connect button
                    document.getElementById('refresh-btn').style.display = 'none';
                    document.getElementById('connect-btn').textContent = 'Generate New QR Code';
                    document.getElementById('connect-btn').style.backgroundColor = '#128C7E';

                    // Show success notification popup
                    showSuccessNotification();

                } else {
                    // Show appropriate waiting message without overriding the QR code
                    const message = data.message || 'Waiting for authentication...';
                    const statusType = data.waiting_for_scan ? 'waiting' : 'warning';

                    // Only update status, don't show "Checking..." during automatic polling
                    if (data.status === 'PENDING') {
                        // Update the scanning indicator in the QR area
                        const scanningIndicator = document.querySelector('.scanning-indicator');
                        if (scanningIndicator) {
                            scanningIndicator.innerHTML = '🔍 Monitoring for scan... (Auto-refresh every 2s)';
                        }
                    }

                    if (data.status === 'ERROR') {
                        showStatus('error', `Error: ${data.error || 'Unknown error occurred'}`);
                        stopAuthCheck(); // Stop checking on error
                    }
                }

            } catch (error) {
                console.error('Error checking status:', error);
                showStatus('error', `Failed to check status: ${error.message}`);
                // Don't stop checking on network errors, might be temporary
            }
        }

        function showStatus(type, message) {
            const statusIndicator = document.getElementById('status-indicator');
            statusIndicator.className = `status-indicator status-${type}`;
            statusIndicator.textContent = message;
            statusIndicator.style.display = 'block';
        }

        function showAccountInfo(data) {
            const accountInfo = document.getElementById('account-info');
            document.getElementById('account-id').textContent = data.account_id || '-';
            document.getElementById('phone-number').textContent = data.phone_number || '-';
            document.getElementById('connection-status').textContent = data.status || 'Connected';
            accountInfo.style.display = 'block';
        }

        function showSuccessNotification() {
            // Create success notification popup
            const notification = document.createElement('div');
            notification.className = 'success-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <div style="color: #25D366; font-size: 32px; margin-bottom: 10px;">✓</div>
                    <h3 style="color: #25D366; margin: 0 0 10px 0;">Connected Successfully!</h3>
                    <p style="margin: 0; color: #666;">Your WhatsApp account is now linked and ready to send messages.</p>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification with animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        }

        function startAuthCheck() {
            // Stop any existing interval
            stopAuthCheck();

            // Show that we're monitoring
            showStatus('waiting', 'QR code generated. Monitoring for scan...');

            // Check status every 2 seconds for faster detection
            authCheckInterval = setInterval(checkStatus, 2000);

            // Also do an immediate check
            setTimeout(checkStatus, 1000);

            console.log('Started automatic authentication monitoring');
        }

        function stopAuthCheck() {
            if (authCheckInterval) {
                clearInterval(authCheckInterval);
                authCheckInterval = null;
            }
        }

        // Check initial status on page load
        window.addEventListener('load', function() {
            // You could add initial status check here if needed
        });

        // Clean up interval when page is closed
        window.addEventListener('beforeunload', function() {
            stopAuthCheck();
        });
    </script>
</body>
</html>

