<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Bot Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0088cc, #229ED9);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #0088cc;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .token-input-area {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #0088cc;
        }
        
        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }
        
        .status-waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .connect-btn {
            background: #0088cc;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .connect-btn:hover {
            background: #006699;
            transform: translateY(-2px);
        }
        
        .connect-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 10px;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            color: #333;
        }
        
        .bot-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            display: none;
        }
        
        .bot-info h4 {
            margin-top: 0;
            color: #333;
        }
        
        .bot-info .info-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .bot-info .info-row:last-child {
            border-bottom: none;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0088cc;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">🤖</div>
        <h1>Telegram Bot Setup</h1>
        <p class="subtitle">Configure your Telegram bot to start sending messages</p>
        
        <div class="token-input-area">
            <div class="form-group">
                <label for="bot-token">Bot Token:</label>
                <input type="text" id="bot-token" placeholder="Enter your bot token from @BotFather" 
                       value="">
            </div>
            
            <button class="connect-btn" id="setup-btn" onclick="setupBot()">
                Setup Bot
            </button>
            <button class="test-btn" id="test-btn" onclick="testBot()" style="display: none;">
                Test Bot
            </button>
        </div>
        
        <div class="status-indicator" id="status-indicator"></div>
        
        <div class="bot-info" id="bot-info">
            <h4>Bot Information</h4>
            <div class="info-row">
                <span><strong>Bot Name:</strong></span>
                <span id="bot-name">-</span>
            </div>
            <div class="info-row">
                <span><strong>Username:</strong></span>
                <span id="bot-username">-</span>
            </div>
            <div class="info-row">
                <span><strong>Bot ID:</strong></span>
                <span id="bot-id">-</span>
            </div>
            <div class="info-row">
                <span><strong>Can Join Groups:</strong></span>
                <span id="can-join-groups">-</span>
            </div>
            <div class="info-row">
                <span><strong>Can Read Messages:</strong></span>
                <span id="can-read-messages">-</span>
            </div>
        </div>
        
        <div class="instructions">
            <h3>How to Create a Telegram Bot:</h3>
            <ol>
                <li>Open Telegram and search for <span class="code">@BotFather</span></li>
                <li>Start a chat with BotFather and send <span class="code">/newbot</span></li>
                <li>Follow the instructions to choose a name and username for your bot</li>
                <li>Copy the bot token provided by BotFather</li>
                <li>Paste the token in the field above and click "Setup Bot"</li>
                <li>Your bot is now ready to send messages!</li>
            </ol>
            
            <h3>Important Notes:</h3>
            <ul>
                <li>Keep your bot token secure and never share it publicly</li>
                <li>Users must start a conversation with your bot before you can send them messages</li>
                <li>For group messaging, add your bot to the group and give it appropriate permissions</li>
            </ul>
        </div>
    </div>

    <script>
        // API base URL - adjust as needed
        const API_BASE = '/api/telegram';
        
        async function setupBot() {
            const botToken = document.getElementById('bot-token').value.trim();
            const setupBtn = document.getElementById('setup-btn');
            const testBtn = document.getElementById('test-btn');
            const statusIndicator = document.getElementById('status-indicator');
            const botInfo = document.getElementById('bot-info');
            
            if (!botToken) {
                showStatus('error', 'Please enter a bot token');
                return;
            }
            
            // Validate token format (basic check)
            if (!botToken.match(/^\d+:[A-Za-z0-9_-]+$/)) {
                showStatus('error', 'Invalid bot token format');
                return;
            }
            
            // Show loading state
            setupBtn.disabled = true;
            setupBtn.textContent = 'Setting up...';
            showStatus('waiting', 'Configuring bot...');
            
            try {
                const response = await fetch(`${API_BASE}/setup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        bot_token: botToken
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'Bot configured successfully!');
                    showBotInfo(data.bot_info);
                    testBtn.style.display = 'inline-block';
                    
                    // Store token in localStorage for convenience
                    localStorage.setItem('telegram_bot_token', botToken);
                    
                } else {
                    showStatus('error', data.message || 'Failed to setup bot');
                    botInfo.style.display = 'none';
                }
                
            } catch (error) {
                console.error('Error setting up bot:', error);
                showStatus('error', 'Network error. Please try again.');
            }
            
            // Reset button
            setupBtn.disabled = false;
            setupBtn.textContent = 'Setup Bot';
        }
        
        async function testBot() {
            const testBtn = document.getElementById('test-btn');
            const originalText = testBtn.textContent;
            
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';
            showStatus('waiting', 'Testing bot connection...');
            
            try {
                const response = await fetch(`${API_BASE}/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'Bot is working correctly!');
                } else {
                    showStatus('error', data.message || 'Bot test failed');
                }
                
            } catch (error) {
                console.error('Error testing bot:', error);
                showStatus('error', 'Failed to test bot connection');
            }
            
            testBtn.disabled = false;
            testBtn.textContent = originalText;
        }
        
        function showStatus(type, message) {
            const statusIndicator = document.getElementById('status-indicator');
            statusIndicator.className = `status-indicator status-${type}`;
            statusIndicator.textContent = message;
            statusIndicator.style.display = 'block';
        }
        
        function showBotInfo(botInfo) {
            const botInfoDiv = document.getElementById('bot-info');
            
            document.getElementById('bot-name').textContent = botInfo.first_name || '-';
            document.getElementById('bot-username').textContent = '@' + (botInfo.username || '-');
            document.getElementById('bot-id').textContent = botInfo.id || '-';
            document.getElementById('can-join-groups').textContent = botInfo.can_join_groups ? 'Yes' : 'No';
            document.getElementById('can-read-messages').textContent = botInfo.can_read_all_group_messages ? 'Yes' : 'No';
            
            botInfoDiv.style.display = 'block';
        }
        
        // Load saved token on page load
        window.addEventListener('load', function() {
            const savedToken = localStorage.getItem('telegram_bot_token');
            if (savedToken) {
                document.getElementById('bot-token').value = savedToken;
            }
        });
        
        // Allow Enter key to setup bot
        document.getElementById('bot-token').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                setupBot();
            }
        });
    </script>
</body>
</html>
