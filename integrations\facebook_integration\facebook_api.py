"""
Facebook Messaging Integration using Graph API and Unipile
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unipile_config import UnipileClient
import json
import requests
from typing import List, Dict, Any, Optional
import time
import urllib.parse


class FacebookMessaging:
    """Facebook messaging class with Graph API and Unipile integration"""
    
    def __init__(self, access_token: str = None, unipile_api_key: str = None):
        """Initialize Facebook messaging"""
        if unipile_api_key is None:
            unipile_api_key = "b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s="
        
        self.access_token = access_token
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.page_id = None
        self.is_authenticated = False
        
        # Facebook Graph API base URL
        self.graph_api_url = "https://graph.facebook.com/v18.0"
        
        # Load configuration
        self.config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        self.load_config()
    
    def load_config(self):
        """Load Facebook configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                if self.config.get("access_token") and not self.access_token:
                    self.access_token = self.config["access_token"]
        except FileNotFoundError:
            self.config = {
                "access_token": None,
                "account_id": None,
                "page_id": None,
                "page_name": None,
                "app_id": None,
                "last_authenticated": None
            }
            self.save_config()
    
    def save_config(self):
        """Save Facebook configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def authenticate_account(self) -> Dict[str, Any]:
        """Authenticate Facebook account via Unipile"""
        try:
            auth_response = self.unipile.authenticate_account("facebook")
            
            if auth_response.get("success"):
                if "account_id" in auth_response:
                    self.account_id = auth_response["account_id"]
                    self.config["account_id"] = self.account_id
                    self.save_config()
                
                return {
                    "success": True,
                    "message": "Facebook account authenticated via Unipile",
                    "account_id": self.account_id,
                    "auth_url": auth_response.get("auth_url"),
                    "auth_data": auth_response
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to authenticate Facebook account",
                    "error": auth_response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Authentication error: {str(e)}"
            }
    
    def setup_page_access(self, access_token: str) -> Dict[str, Any]:
        """
        Setup Facebook page access with access token
        Args:
            access_token: Facebook page access token
        """
        self.access_token = access_token
        self.config["access_token"] = access_token
        
        try:
            # Get page information
            response = requests.get(
                f"{self.graph_api_url}/me",
                params={"access_token": access_token}
            )
            
            if response.status_code == 200:
                page_data = response.json()
                self.page_id = page_data.get("id")
                self.config["page_id"] = self.page_id
                self.config["page_name"] = page_data.get("name")
                self.config["last_authenticated"] = time.time()
                self.is_authenticated = True
                self.save_config()
                
                return {
                    "success": True,
                    "message": "Facebook page access configured",
                    "page_data": page_data
                }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Invalid access token: {error_data.get('error', {}).get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Page setup error: {str(e)}"
            }
    
    def send_message(self, recipient_id: str, message: str, message_type: str = "text") -> Dict[str, Any]:
        """
        Send message via Facebook Messenger
        Args:
            recipient_id: Facebook user ID (PSID)
            message: Message text to send
            message_type: Type of message (text, image, etc.)
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "Facebook access token not configured"
            }
        
        try:
            # Prepare message data
            message_data = {
                "recipient": {"id": recipient_id},
                "message": {"text": message}
            }
            
            # Send via Messenger API
            response = requests.post(
                f"{self.graph_api_url}/me/messages",
                params={"access_token": self.access_token},
                json=message_data
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "Message sent successfully",
                    "message_id": result.get("message_id"),
                    "recipient_id": result.get("recipient_id")
                }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Failed to send message: {error_data.get('error', {}).get('message', 'Unknown error')}",
                    "error_code": error_data.get('error', {}).get('code')
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Send message error: {str(e)}"
            }
    
    def send_message_unipile(self, recipient_id: str, message: str) -> Dict[str, Any]:
        """Send message via Unipile API"""
        if not self.account_id:
            return {
                "success": False,
                "message": "Unipile account not authenticated"
            }
        
        try:
            response = self.unipile.send_message(
                account_id=self.account_id,
                recipient=recipient_id,
                message=message
            )
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": "Message sent via Unipile",
                    "message_id": response.get("message_id"),
                    "recipient": recipient_id
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send message via Unipile",
                    "error": response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Unipile send error: {str(e)}"
            }
    
    def send_bulk_messages(self, recipients: List[Dict[str, str]], message: str) -> Dict[str, Any]:
        """
        Send bulk messages to multiple recipients
        Args:
            recipients: List of recipient data [{"user_id": "123456", "name": "John"}]
            message: Message text to send
        """
        if not self.access_token and not self.account_id:
            return {
                "success": False,
                "message": "Facebook not configured"
            }
        
        results = []
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            user_id = recipient.get("user_id") or recipient.get("recipient_id")
            if not user_id:
                results.append({
                    "recipient": recipient,
                    "success": False,
                    "message": "No user ID provided"
                })
                failed_sends += 1
                continue
            
            # Personalize message if name is provided
            personalized_message = message
            if recipient.get("name"):
                personalized_message = f"Hi {recipient['name']}, {message}"
            
            # Try Unipile first, fallback to Graph API
            send_result = None
            if self.account_id:
                send_result = self.send_message_unipile(user_id, personalized_message)
            
            if not send_result or not send_result.get("success"):
                send_result = self.send_message(user_id, personalized_message)
            
            results.append({
                "recipient": recipient,
                "success": send_result["success"],
                "message": send_result["message"],
                "message_id": send_result.get("message_id")
            })
            
            if send_result["success"]:
                successful_sends += 1
            else:
                failed_sends += 1
            
            # Add delay to avoid rate limiting
            time.sleep(1)
        
        return {
            "success": True,
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "results": results
        }
    
    def get_page_info(self) -> Dict[str, Any]:
        """Get information about the connected Facebook page"""
        if not self.access_token:
            return {
                "success": False,
                "message": "No access token configured"
            }
        
        try:
            response = requests.get(
                f"{self.graph_api_url}/me",
                params={
                    "access_token": self.access_token,
                    "fields": "id,name,category,followers_count,fan_count"
                }
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "page_info": response.json()
                }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Failed to get page info: {error_data.get('error', {}).get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting page info: {str(e)}"
            }
    
    def generate_oauth_url(self, app_id: str, redirect_uri: str, state: str = None) -> str:
        """
        Generate OAuth URL for Facebook authentication
        Args:
            app_id: Facebook App ID
            redirect_uri: Redirect URI after authentication
            state: Optional state parameter for security
        """
        base_url = "https://www.facebook.com/v18.0/dialog/oauth"
        
        params = {
            "client_id": app_id,
            "redirect_uri": redirect_uri,
            "scope": "pages_messaging,pages_manage_metadata,pages_read_engagement",
            "response_type": "code"
        }
        
        if state:
            params["state"] = state
        
        return f"{base_url}?{urllib.parse.urlencode(params)}"
    
    def exchange_code_for_token(self, app_id: str, app_secret: str, redirect_uri: str, code: str) -> Dict[str, Any]:
        """
        Exchange authorization code for access token
        Args:
            app_id: Facebook App ID
            app_secret: Facebook App Secret
            redirect_uri: Redirect URI used in OAuth
            code: Authorization code from Facebook
        """
        try:
            response = requests.get(
                f"{self.graph_api_url}/oauth/access_token",
                params={
                    "client_id": app_id,
                    "client_secret": app_secret,
                    "redirect_uri": redirect_uri,
                    "code": code
                }
            )
            
            if response.status_code == 200:
                token_data = response.json()
                return {
                    "success": True,
                    "access_token": token_data.get("access_token"),
                    "token_type": token_data.get("token_type"),
                    "expires_in": token_data.get("expires_in")
                }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Token exchange failed: {error_data.get('error', {}).get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Token exchange error: {str(e)}"
            }
