<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #1877f2;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .auth-methods {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }
        
        .auth-method {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }
        
        .auth-method h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #1877f2;
        }
        
        .connect-btn {
            background: #1877f2;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .connect-btn:hover {
            background: #166fe5;
            transform: translateY(-2px);
        }
        
        .connect-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .oauth-btn {
            background: #42a5f5;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .oauth-btn:hover {
            background: #1e88e5;
            transform: translateY(-2px);
        }
        
        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }
        
        .status-waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .page-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            display: none;
        }
        
        .page-info h4 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">📘</div>
        <h1>Facebook Authentication</h1>
        <p class="subtitle">Connect your Facebook page to start sending messages</p>
        
        <div class="auth-methods">
            <!-- OAuth Method -->
            <div class="auth-method">
                <h3>Method 1: OAuth Authentication (Recommended)</h3>
                <p>Use Facebook's OAuth flow for secure authentication</p>
                
                <div class="form-group">
                    <label for="app-id">Facebook App ID:</label>
                    <input type="text" id="app-id" placeholder="Enter your Facebook App ID">
                </div>
                
                <button class="oauth-btn" id="oauth-btn" onclick="startOAuth()">
                    Connect with Facebook
                </button>
            </div>
            
            <!-- Direct Token Method -->
            <div class="auth-method">
                <h3>Method 2: Direct Access Token</h3>
                <p>Enter a Facebook page access token directly</p>
                
                <div class="form-group">
                    <label for="access-token">Page Access Token:</label>
                    <input type="text" id="access-token" placeholder="Enter your page access token">
                </div>
                
                <button class="connect-btn" id="token-btn" onclick="setupWithToken()">
                    Setup with Token
                </button>
                <button class="connect-btn" id="test-btn" onclick="testConnection()" style="display: none;">
                    Test Connection
                </button>
            </div>
            
            <!-- Unipile Method -->
            <div class="auth-method">
                <h3>Method 3: Unipile Integration</h3>
                <p>Use Unipile for simplified Facebook integration</p>
                
                <button class="connect-btn" id="unipile-btn" onclick="authenticateUnipile()">
                    Connect via Unipile
                </button>
            </div>
        </div>
        
        <div class="status-indicator" id="status-indicator"></div>
        
        <div class="page-info" id="page-info">
            <h4>Connected Page Information</h4>
            <p><strong>Page Name:</strong> <span id="page-name">-</span></p>
            <p><strong>Page ID:</strong> <span id="page-id">-</span></p>
            <p><strong>Category:</strong> <span id="page-category">-</span></p>
            <p><strong>Followers:</strong> <span id="page-followers">-</span></p>
        </div>
        
        <div class="instructions">
            <h3>Setup Instructions:</h3>
            
            <h4>For OAuth Authentication:</h4>
            <ol>
                <li>Create a Facebook App at <a href="https://developers.facebook.com" target="_blank">developers.facebook.com</a></li>
                <li>Add the Messenger platform to your app</li>
                <li>Get your App ID from the app dashboard</li>
                <li>Enter the App ID above and click "Connect with Facebook"</li>
                <li>Complete the OAuth flow to authorize your page</li>
            </ol>
            
            <h4>For Direct Token:</h4>
            <ol>
                <li>Go to <a href="https://developers.facebook.com/tools/explorer" target="_blank">Graph API Explorer</a></li>
                <li>Select your app and generate a page access token</li>
                <li>Grant necessary permissions: <span class="code">pages_messaging</span>, <span class="code">pages_manage_metadata</span></li>
                <li>Copy the token and paste it above</li>
            </ol>
            
            <h4>Required Permissions:</h4>
            <ul>
                <li><span class="code">pages_messaging</span> - Send messages to users</li>
                <li><span class="code">pages_manage_metadata</span> - Manage page information</li>
                <li><span class="code">pages_read_engagement</span> - Read page engagement data</li>
            </ul>
        </div>
    </div>

    <script>
        // API base URL - adjust as needed
        const API_BASE = '/api/facebook';
        
        async function startOAuth() {
            const appId = document.getElementById('app-id').value.trim();
            
            if (!appId) {
                showStatus('error', 'Please enter your Facebook App ID');
                return;
            }
            
            showStatus('waiting', 'Redirecting to Facebook...');
            
            try {
                // Generate OAuth URL
                const redirectUri = encodeURIComponent(window.location.origin + '/facebook-callback');
                const state = Math.random().toString(36).substring(7);
                
                // Store state for verification
                localStorage.setItem('facebook_oauth_state', state);
                localStorage.setItem('facebook_app_id', appId);
                
                const oauthUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${appId}&redirect_uri=${redirectUri}&scope=pages_messaging,pages_manage_metadata,pages_read_engagement&response_type=code&state=${state}`;
                
                // Redirect to Facebook OAuth
                window.location.href = oauthUrl;
                
            } catch (error) {
                console.error('OAuth error:', error);
                showStatus('error', 'Failed to start OAuth flow');
            }
        }
        
        async function setupWithToken() {
            const accessToken = document.getElementById('access-token').value.trim();
            const tokenBtn = document.getElementById('token-btn');
            const testBtn = document.getElementById('test-btn');
            
            if (!accessToken) {
                showStatus('error', 'Please enter an access token');
                return;
            }
            
            tokenBtn.disabled = true;
            tokenBtn.textContent = 'Setting up...';
            showStatus('waiting', 'Configuring Facebook page access...');
            
            try {
                const response = await fetch(`${API_BASE}/setup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        access_token: accessToken
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'Facebook page configured successfully!');
                    showPageInfo(data.page_data);
                    testBtn.style.display = 'inline-block';
                    
                    // Store token for convenience
                    localStorage.setItem('facebook_access_token', accessToken);
                    
                } else {
                    showStatus('error', data.message || 'Failed to setup Facebook page');
                }
                
            } catch (error) {
                console.error('Setup error:', error);
                showStatus('error', 'Network error. Please try again.');
            }
            
            tokenBtn.disabled = false;
            tokenBtn.textContent = 'Setup with Token';
        }
        
        async function authenticateUnipile() {
            const unipileBtn = document.getElementById('unipile-btn');
            
            unipileBtn.disabled = true;
            unipileBtn.textContent = 'Connecting...';
            showStatus('waiting', 'Authenticating via Unipile...');
            
            try {
                const response = await fetch(`${API_BASE}/authenticate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    if (data.auth_url) {
                        showStatus('waiting', 'Redirecting to authentication...');
                        window.open(data.auth_url, '_blank');
                    } else {
                        showStatus('success', 'Facebook account authenticated via Unipile!');
                    }
                } else {
                    showStatus('error', data.message || 'Failed to authenticate via Unipile');
                }
                
            } catch (error) {
                console.error('Unipile auth error:', error);
                showStatus('error', 'Failed to connect via Unipile');
            }
            
            unipileBtn.disabled = false;
            unipileBtn.textContent = 'Connect via Unipile';
        }
        
        async function testConnection() {
            const testBtn = document.getElementById('test-btn');
            const originalText = testBtn.textContent;
            
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';
            showStatus('waiting', 'Testing Facebook connection...');
            
            try {
                const response = await fetch(`${API_BASE}/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'Facebook connection is working correctly!');
                    if (data.page_info) {
                        showPageInfo(data.page_info);
                    }
                } else {
                    showStatus('error', data.message || 'Connection test failed');
                }
                
            } catch (error) {
                console.error('Test error:', error);
                showStatus('error', 'Failed to test connection');
            }
            
            testBtn.disabled = false;
            testBtn.textContent = originalText;
        }
        
        function showStatus(type, message) {
            const statusIndicator = document.getElementById('status-indicator');
            statusIndicator.className = `status-indicator status-${type}`;
            statusIndicator.textContent = message;
            statusIndicator.style.display = 'block';
        }
        
        function showPageInfo(pageData) {
            const pageInfo = document.getElementById('page-info');
            
            document.getElementById('page-name').textContent = pageData.name || '-';
            document.getElementById('page-id').textContent = pageData.id || '-';
            document.getElementById('page-category').textContent = pageData.category || '-';
            document.getElementById('page-followers').textContent = pageData.followers_count || pageData.fan_count || '-';
            
            pageInfo.style.display = 'block';
        }
        
        // Handle OAuth callback
        function handleOAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const storedState = localStorage.getItem('facebook_oauth_state');
            
            if (code && state && state === storedState) {
                const appId = localStorage.getItem('facebook_app_id');
                
                // Exchange code for token
                exchangeCodeForToken(appId, code);
                
                // Clean up
                localStorage.removeItem('facebook_oauth_state');
                localStorage.removeItem('facebook_app_id');
            }
        }
        
        async function exchangeCodeForToken(appId, code) {
            showStatus('waiting', 'Exchanging authorization code for access token...');
            
            try {
                const response = await fetch(`${API_BASE}/oauth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        app_id: appId,
                        code: code,
                        redirect_uri: window.location.origin + '/facebook-callback'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'OAuth authentication successful!');
                    document.getElementById('access-token').value = data.access_token;
                    setupWithToken();
                } else {
                    showStatus('error', data.message || 'OAuth token exchange failed');
                }
                
            } catch (error) {
                console.error('Token exchange error:', error);
                showStatus('error', 'Failed to exchange authorization code');
            }
        }
        
        // Load saved values on page load
        window.addEventListener('load', function() {
            const savedToken = localStorage.getItem('facebook_access_token');
            if (savedToken) {
                document.getElementById('access-token').value = savedToken;
            }
            
            // Check if this is an OAuth callback
            if (window.location.search.includes('code=')) {
                handleOAuthCallback();
            }
        });
    </script>
</body>
</html>
