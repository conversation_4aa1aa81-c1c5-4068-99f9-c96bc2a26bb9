"""
TikTok Messaging Integration using TikTok for Business API and Unipile
Note: TikTok has limited messaging capabilities - focus on comments and interactions
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unipile_config import UnipileClient
import json
import requests
from typing import List, Dict, Any, Optional
import time
import urllib.parse
import hashlib
import hmac


class TikTokMessaging:
    """TikTok messaging class with Business API and Unipile integration"""
    
    def __init__(self, access_token: str = None, unipile_api_key: str = None):
        """Initialize TikTok messaging"""
        if unipile_api_key is None:
            unipile_api_key = "b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s="
        
        self.access_token = access_token
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.business_account_id = None
        self.is_authenticated = False
        
        # TikTok Business API base URL
        self.api_base_url = "https://business-api.tiktok.com/open_api/v1.3"
        
        # Load configuration
        self.config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        self.load_config()
    
    def load_config(self):
        """Load TikTok configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                if self.config.get("access_token") and not self.access_token:
                    self.access_token = self.config["access_token"]
        except FileNotFoundError:
            self.config = {
                "access_token": None,
                "account_id": None,
                "business_account_id": None,
                "app_id": None,
                "app_secret": None,
                "last_authenticated": None
            }
            self.save_config()
    
    def save_config(self):
        """Save TikTok configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def authenticate_account(self) -> Dict[str, Any]:
        """Authenticate TikTok account via Unipile"""
        try:
            auth_response = self.unipile.authenticate_account("tiktok")
            
            if auth_response.get("success"):
                if "account_id" in auth_response:
                    self.account_id = auth_response["account_id"]
                    self.config["account_id"] = self.account_id
                    self.save_config()
                
                return {
                    "success": True,
                    "message": "TikTok account authenticated via Unipile",
                    "account_id": self.account_id,
                    "auth_url": auth_response.get("auth_url"),
                    "auth_data": auth_response
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to authenticate TikTok account",
                    "error": auth_response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Authentication error: {str(e)}"
            }
    
    def setup_business_account(self, access_token: str, app_id: str = None, app_secret: str = None) -> Dict[str, Any]:
        """
        Setup TikTok Business account access
        Args:
            access_token: TikTok access token
            app_id: TikTok app ID (optional)
            app_secret: TikTok app secret (optional)
        """
        self.access_token = access_token
        self.config["access_token"] = access_token
        
        if app_id:
            self.config["app_id"] = app_id
        if app_secret:
            self.config["app_secret"] = app_secret
        
        try:
            # Get user info to verify token
            headers = {
                "Access-Token": access_token,
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.api_base_url}/user/info/",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:  # TikTok API success code
                    user_data = data.get("data", {})
                    self.business_account_id = user_data.get("open_id")
                    self.config["business_account_id"] = self.business_account_id
                    self.config["last_authenticated"] = time.time()
                    self.is_authenticated = True
                    self.save_config()
                    
                    return {
                        "success": True,
                        "message": "TikTok Business account configured",
                        "user_data": user_data
                    }
                else:
                    return {
                        "success": False,
                        "message": f"TikTok API error: {data.get('message', 'Unknown error')}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"HTTP error: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Account setup error: {str(e)}"
            }
    
    def send_message(self, recipient_id: str, message: str) -> Dict[str, Any]:
        """
        Send message via TikTok (Limited functionality)
        Note: TikTok doesn't have direct messaging API. This uses Unipile.
        """
        # TikTok Business API doesn't support direct messaging
        # Try Unipile integration
        if self.account_id:
            return self.send_message_unipile(recipient_id, message)
        else:
            return {
                "success": False,
                "message": "TikTok direct messaging requires Unipile integration"
            }
    
    def send_message_unipile(self, recipient_id: str, message: str) -> Dict[str, Any]:
        """Send message via Unipile API"""
        if not self.account_id:
            return {
                "success": False,
                "message": "Unipile account not authenticated"
            }
        
        try:
            response = self.unipile.send_message(
                account_id=self.account_id,
                recipient=recipient_id,
                message=message
            )
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": "Message sent via Unipile",
                    "message_id": response.get("message_id"),
                    "recipient": recipient_id
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send message via Unipile",
                    "error": response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Unipile send error: {str(e)}"
            }
    
    def reply_to_comment(self, video_id: str, comment_id: str, reply_text: str) -> Dict[str, Any]:
        """
        Reply to a comment on TikTok video
        Args:
            video_id: TikTok video ID
            comment_id: Comment ID to reply to
            reply_text: Reply text
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "TikTok access token not configured"
            }
        
        try:
            headers = {
                "Access-Token": self.access_token,
                "Content-Type": "application/json"
            }
            
            data = {
                "video_id": video_id,
                "comment_id": comment_id,
                "text": reply_text
            }
            
            response = requests.post(
                f"{self.api_base_url}/comment/reply/",
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return {
                        "success": True,
                        "message": "Comment reply sent successfully",
                        "reply_data": result.get("data", {})
                    }
                else:
                    return {
                        "success": False,
                        "message": f"TikTok API error: {result.get('message', 'Unknown error')}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"HTTP error: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Comment reply error: {str(e)}"
            }
    
    def send_bulk_messages(self, recipients: List[Dict[str, str]], message: str) -> Dict[str, Any]:
        """
        Send bulk messages (primarily via Unipile)
        Args:
            recipients: List of recipient data
            message: Message text to send
        """
        if not self.account_id:
            return {
                "success": False,
                "message": "TikTok messaging requires Unipile authentication"
            }
        
        results = []
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            user_id = recipient.get("user_id") or recipient.get("username")
            if not user_id:
                results.append({
                    "recipient": recipient,
                    "success": False,
                    "message": "No user ID or username provided"
                })
                failed_sends += 1
                continue
            
            # Personalize message if name is provided
            personalized_message = message
            if recipient.get("name"):
                personalized_message = f"Hi {recipient['name']}, {message}"
            
            # Send via Unipile
            send_result = self.send_message_unipile(user_id, personalized_message)
            
            results.append({
                "recipient": recipient,
                "success": send_result["success"],
                "message": send_result["message"],
                "message_id": send_result.get("message_id")
            })
            
            if send_result["success"]:
                successful_sends += 1
            else:
                failed_sends += 1
            
            # Add delay to avoid rate limiting
            time.sleep(2)  # TikTok has strict rate limits
        
        return {
            "success": True,
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "results": results
        }
    
    def get_user_info(self) -> Dict[str, Any]:
        """Get TikTok user information"""
        if not self.access_token:
            return {
                "success": False,
                "message": "TikTok access token not configured"
            }
        
        try:
            headers = {
                "Access-Token": self.access_token,
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.api_base_url}/user/info/",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return {
                        "success": True,
                        "user_info": data.get("data", {})
                    }
                else:
                    return {
                        "success": False,
                        "message": f"TikTok API error: {data.get('message', 'Unknown error')}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"HTTP error: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting user info: {str(e)}"
            }
    
    def get_video_comments(self, video_id: str, cursor: str = "0", count: int = 20) -> Dict[str, Any]:
        """Get comments for a specific video"""
        if not self.access_token:
            return {
                "success": False,
                "message": "TikTok access token not configured"
            }
        
        try:
            headers = {
                "Access-Token": self.access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "video_id": video_id,
                "cursor": cursor,
                "count": count
            }
            
            response = requests.get(
                f"{self.api_base_url}/comment/list/",
                headers=headers,
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return {
                        "success": True,
                        "comments": data.get("data", {})
                    }
                else:
                    return {
                        "success": False,
                        "message": f"TikTok API error: {data.get('message', 'Unknown error')}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"HTTP error: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting comments: {str(e)}"
            }
    
    def generate_oauth_url(self, client_key: str, redirect_uri: str, state: str = None) -> str:
        """Generate OAuth URL for TikTok authentication"""
        base_url = "https://www.tiktok.com/auth/authorize/"
        
        params = {
            "client_key": client_key,
            "response_type": "code",
            "scope": "user.info.basic,video.list",
            "redirect_uri": redirect_uri
        }
        
        if state:
            params["state"] = state
        
        return f"{base_url}?{urllib.parse.urlencode(params)}"
    
    def exchange_code_for_token(self, client_key: str, client_secret: str, code: str, redirect_uri: str) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        try:
            data = {
                "client_key": client_key,
                "client_secret": client_secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": redirect_uri
            }
            
            response = requests.post(
                "https://open-api.tiktok.com/oauth/access_token/",
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("data"):
                    return {
                        "success": True,
                        "access_token": result["data"].get("access_token"),
                        "refresh_token": result["data"].get("refresh_token"),
                        "expires_in": result["data"].get("expires_in"),
                        "scope": result["data"].get("scope")
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Token exchange failed: {result.get('message', 'Unknown error')}"
                    }
            else:
                return {
                    "success": False,
                    "message": f"HTTP error: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Token exchange error: {str(e)}"
            }
