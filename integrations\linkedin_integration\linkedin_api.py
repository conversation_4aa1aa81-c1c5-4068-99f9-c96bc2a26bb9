"""
LinkedIn Messaging Integration using LinkedIn Marketing Developer Platform and Unipile
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unipile_config import UnipileClient
import json
import requests
from typing import List, Dict, Any, Optional
import time
import urllib.parse


class LinkedInMessaging:
    """LinkedIn messaging class with Marketing API and Unipile integration"""
    
    def __init__(self, access_token: str = None, unipile_api_key: str = None):
        """Initialize LinkedIn messaging"""
        if unipile_api_key is None:
            unipile_api_key = "b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s="
        
        self.access_token = access_token
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.person_id = None
        self.is_authenticated = False
        
        # LinkedIn API base URL
        self.api_base_url = "https://api.linkedin.com/v2"
        
        # Load configuration
        self.config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        self.load_config()
    
    def load_config(self):
        """Load LinkedIn configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                if self.config.get("access_token") and not self.access_token:
                    self.access_token = self.config["access_token"]
        except FileNotFoundError:
            self.config = {
                "access_token": None,
                "account_id": None,
                "person_id": None,
                "profile_info": None,
                "last_authenticated": None
            }
            self.save_config()
    
    def save_config(self):
        """Save LinkedIn configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def authenticate_account(self) -> Dict[str, Any]:
        """Authenticate LinkedIn account via Unipile"""
        try:
            auth_response = self.unipile.authenticate_account("linkedin")
            
            if auth_response.get("success"):
                if "account_id" in auth_response:
                    self.account_id = auth_response["account_id"]
                    self.config["account_id"] = self.account_id
                    self.save_config()
                
                return {
                    "success": True,
                    "message": "LinkedIn account authenticated via Unipile",
                    "account_id": self.account_id,
                    "auth_url": auth_response.get("auth_url"),
                    "auth_data": auth_response
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to authenticate LinkedIn account",
                    "error": auth_response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Authentication error: {str(e)}"
            }
    
    def setup_api_access(self, access_token: str) -> Dict[str, Any]:
        """
        Setup LinkedIn API access with access token
        Args:
            access_token: LinkedIn access token
        """
        self.access_token = access_token
        self.config["access_token"] = access_token
        
        try:
            # Get profile information to verify token
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.api_base_url}/people/~:(id,firstName,lastName,emailAddress)",
                headers=headers
            )
            
            if response.status_code == 200:
                profile_data = response.json()
                self.person_id = profile_data.get("id")
                self.config["person_id"] = self.person_id
                self.config["profile_info"] = profile_data
                self.config["last_authenticated"] = time.time()
                self.is_authenticated = True
                self.save_config()
                
                return {
                    "success": True,
                    "message": "LinkedIn API access configured",
                    "profile_data": profile_data
                }
            else:
                error_data = response.json() if response.content else {}
                return {
                    "success": False,
                    "message": f"Invalid access token: {error_data.get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"API setup error: {str(e)}"
            }
    
    def send_message(self, recipient_id: str, message: str, subject: str = None) -> Dict[str, Any]:
        """
        Send message via LinkedIn (Limited to connections)
        Args:
            recipient_id: LinkedIn person ID
            message: Message text
            subject: Message subject (optional)
        """
        # LinkedIn messaging API is very restricted
        # Try Unipile first
        if self.account_id:
            return self.send_message_unipile(recipient_id, message)
        else:
            return {
                "success": False,
                "message": "LinkedIn messaging requires Unipile integration or special API access"
            }
    
    def send_message_unipile(self, recipient_id: str, message: str) -> Dict[str, Any]:
        """Send message via Unipile API"""
        if not self.account_id:
            return {
                "success": False,
                "message": "Unipile account not authenticated"
            }
        
        try:
            response = self.unipile.send_message(
                account_id=self.account_id,
                recipient=recipient_id,
                message=message
            )
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": "Message sent via Unipile",
                    "message_id": response.get("message_id"),
                    "recipient": recipient_id
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send message via Unipile",
                    "error": response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Unipile send error: {str(e)}"
            }
    
    def send_connection_request(self, recipient_id: str, message: str = None) -> Dict[str, Any]:
        """
        Send connection request with optional message
        Args:
            recipient_id: LinkedIn person ID
            message: Connection request message (optional)
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "LinkedIn access token not configured"
            }
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            data = {
                "invitee": {
                    "com.linkedin.voyager.growth.invitation.InviteeProfile": {
                        "profileId": recipient_id
                    }
                }
            }
            
            if message:
                data["message"] = message
            
            response = requests.post(
                f"{self.api_base_url}/people/~/mailbox",
                headers=headers,
                json=data
            )
            
            if response.status_code in [200, 201]:
                return {
                    "success": True,
                    "message": "Connection request sent successfully"
                }
            else:
                error_data = response.json() if response.content else {}
                return {
                    "success": False,
                    "message": f"Failed to send connection request: {error_data.get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Connection request error: {str(e)}"
            }
    
    def send_inmail(self, recipient_id: str, subject: str, message: str) -> Dict[str, Any]:
        """
        Send InMail message (requires premium features)
        Args:
            recipient_id: LinkedIn person ID
            subject: InMail subject
            message: InMail message
        """
        if not self.access_token:
            return {
                "success": False,
                "message": "LinkedIn access token not configured"
            }
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            data = {
                "recipients": [recipient_id],
                "subject": subject,
                "body": message
            }
            
            response = requests.post(
                f"{self.api_base_url}/messages",
                headers=headers,
                json=data
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                return {
                    "success": True,
                    "message": "InMail sent successfully",
                    "message_id": result.get("id")
                }
            else:
                error_data = response.json() if response.content else {}
                return {
                    "success": False,
                    "message": f"Failed to send InMail: {error_data.get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"InMail error: {str(e)}"
            }
    
    def send_bulk_messages(self, recipients: List[Dict[str, str]], message: str, message_type: str = "connection") -> Dict[str, Any]:
        """
        Send bulk messages to multiple recipients
        Args:
            recipients: List of recipient data
            message: Message text to send
            message_type: Type of message (connection, inmail, direct)
        """
        if not self.account_id and not self.access_token:
            return {
                "success": False,
                "message": "LinkedIn not configured"
            }
        
        results = []
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            person_id = recipient.get("person_id") or recipient.get("linkedin_id")
            if not person_id:
                results.append({
                    "recipient": recipient,
                    "success": False,
                    "message": "No LinkedIn person ID provided"
                })
                failed_sends += 1
                continue
            
            # Personalize message if name is provided
            personalized_message = message
            if recipient.get("name"):
                personalized_message = f"Hi {recipient['name']}, {message}"
            
            # Send based on message type
            if message_type == "connection":
                send_result = self.send_connection_request(person_id, personalized_message)
            elif message_type == "inmail":
                subject = recipient.get("subject", "Professional Opportunity")
                send_result = self.send_inmail(person_id, subject, personalized_message)
            else:  # direct message
                send_result = self.send_message_unipile(person_id, personalized_message)
            
            results.append({
                "recipient": recipient,
                "success": send_result["success"],
                "message": send_result["message"],
                "message_id": send_result.get("message_id")
            })
            
            if send_result["success"]:
                successful_sends += 1
            else:
                failed_sends += 1
            
            # Add delay to avoid rate limiting (LinkedIn is strict)
            time.sleep(3)
        
        return {
            "success": True,
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "results": results
        }
    
    def get_profile_info(self) -> Dict[str, Any]:
        """Get LinkedIn profile information"""
        if not self.access_token:
            return {
                "success": False,
                "message": "LinkedIn access token not configured"
            }
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.api_base_url}/people/~:(id,firstName,lastName,emailAddress,headline,industry,positions)",
                headers=headers
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "profile_info": response.json()
                }
            else:
                error_data = response.json() if response.content else {}
                return {
                    "success": False,
                    "message": f"Failed to get profile info: {error_data.get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting profile info: {str(e)}"
            }
    
    def search_people(self, keywords: str, limit: int = 25) -> Dict[str, Any]:
        """Search for people on LinkedIn"""
        if not self.access_token:
            return {
                "success": False,
                "message": "LinkedIn access token not configured"
            }
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            params = {
                "keywords": keywords,
                "count": limit
            }
            
            response = requests.get(
                f"{self.api_base_url}/people-search",
                headers=headers,
                params=params
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "search_results": response.json()
                }
            else:
                error_data = response.json() if response.content else {}
                return {
                    "success": False,
                    "message": f"Search failed: {error_data.get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Search error: {str(e)}"
            }
    
    def generate_oauth_url(self, client_id: str, redirect_uri: str, state: str = None) -> str:
        """Generate OAuth URL for LinkedIn authentication"""
        base_url = "https://www.linkedin.com/oauth/v2/authorization"
        
        params = {
            "response_type": "code",
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "scope": "r_liteprofile r_emailaddress w_member_social"
        }
        
        if state:
            params["state"] = state
        
        return f"{base_url}?{urllib.parse.urlencode(params)}"
