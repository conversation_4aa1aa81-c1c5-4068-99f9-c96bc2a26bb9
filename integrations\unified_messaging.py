"""
Unified Messaging System
Main controller for all social media platform integrations
"""
import sys
import os

# Add integrations to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from whatsapp_integration import WhatsAppMessaging
from telegram_integration import TelegramMessaging
from facebook_integration import FacebookMessaging
from instagram_integration import InstagramMessaging
from tiktok_integration import TikTokMessaging
from linkedin_integration import LinkedInMessaging
from unipile_config import UnipileConfig

from typing import Dict, List, Any, Optional
import json
import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UnifiedMessaging:
    """Unified messaging controller for all social media platforms"""
    
    def __init__(self, unipile_api_key: str = None):
        """Initialize unified messaging system"""
        if unipile_api_key is None:
            # Use the working API key
            unipile_api_key = "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="
        
        self.unipile_config = UnipileConfig()
        
        # Initialize platform integrations
        self.whatsapp = WhatsAppMessaging(unipile_api_key)
        self.telegram = TelegramMessaging(unipile_api_key=unipile_api_key)
        self.facebook = FacebookMessaging(unipile_api_key=unipile_api_key)
        self.instagram = InstagramMessaging(unipile_api_key=unipile_api_key)
        self.tiktok = TikTokMessaging(unipile_api_key=unipile_api_key)
        self.linkedin = LinkedInMessaging(unipile_api_key=unipile_api_key)
        
        # Platform mapping
        self.platforms = {
            'whatsapp': self.whatsapp,
            'telegram': self.telegram,
            'facebook': self.facebook,
            'instagram': self.instagram,
            'tiktok': self.tiktok,
            'linkedin': self.linkedin
        }
        
        # Platform handlers for sending messages
        self.platform_handlers = {
            'whatsapp': self.whatsapp.send_message,
            'telegram': self.telegram.send_message,
            'facebook': self.facebook.send_message,
            'instagram': self.instagram.send_message,
            'tiktok': self.tiktok.send_message,
            'linkedin': self.linkedin.send_message
        }
        
        # Bulk message handlers
        self.bulk_handlers = {
            'whatsapp': self.whatsapp.send_bulk_messages,
            'telegram': self.telegram.send_bulk_messages,
            'facebook': self.facebook.send_bulk_messages,
            'instagram': self.instagram.send_bulk_messages,
            'tiktok': self.tiktok.send_bulk_messages,
            'linkedin': self.linkedin.send_bulk_messages
        }
    
    def send_message_by_platform(self, platform: str, recipient: str, message: str, **kwargs) -> Dict[str, Any]:
        """
        Send message on specified platform
        Args:
            platform: Platform name (whatsapp, telegram, facebook, etc.)
            recipient: Recipient identifier (phone, user_id, etc.)
            message: Message text to send
            **kwargs: Platform-specific parameters
        """
        if platform not in self.platform_handlers:
            return {
                "success": False,
                "message": f"Platform '{platform}' not supported",
                "supported_platforms": list(self.platform_handlers.keys())
            }
        
        try:
            logger.info(f"Sending message via {platform} to {recipient}")
            
            # Get platform handler
            handler = self.platform_handlers[platform]
            
            # Send message with platform-specific parameters
            if platform == 'telegram':
                result = handler(recipient, message, kwargs.get('parse_mode', 'HTML'))
            elif platform == 'linkedin':
                result = handler(recipient, message, kwargs.get('subject'))
            else:
                result = handler(recipient, message)
            
            # Log result
            if result.get("success"):
                logger.info(f"Message sent successfully via {platform}")
            else:
                logger.error(f"Failed to send message via {platform}: {result.get('message')}")
            
            return result
            
        except Exception as e:
            error_msg = f"Error sending message via {platform}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }
    
    def send_bulk_campaign(self, campaign_data: Dict[str, List[Dict[str, str]]], 
                          default_message: str = None) -> Dict[str, Any]:
        """
        Send messages to multiple recipients across platforms
        Args:
            campaign_data: Dictionary with platform as key and list of recipients as value
                          Format: {
                              "whatsapp": [
                                  {"phone": "+1234567890", "message": "Hello!", "name": "John"},
                                  {"phone": "+0987654321", "message": "Hi there!", "name": "Jane"}
                              ],
                              "telegram": [
                                  {"user_id": "@username", "message": "Hey!", "name": "Bob"}
                              ]
                          }
            default_message: Default message if not specified per recipient
        """
        results = {}
        total_sent = 0
        total_failed = 0
        
        logger.info(f"Starting bulk campaign across {len(campaign_data)} platforms")
        
        for platform, recipients in campaign_data.items():
            if platform not in self.bulk_handlers:
                results[platform] = {
                    "success": False,
                    "message": f"Platform '{platform}' not supported",
                    "results": []
                }
                continue
            
            try:
                logger.info(f"Processing {len(recipients)} recipients for {platform}")
                
                # Prepare recipients with default message if needed
                processed_recipients = []
                for recipient in recipients:
                    if not recipient.get("message") and default_message:
                        recipient["message"] = default_message
                    processed_recipients.append(recipient)
                
                # Get bulk handler and send messages
                bulk_handler = self.bulk_handlers[platform]
                
                # Extract message for bulk sending (use first recipient's message or default)
                bulk_message = processed_recipients[0].get("message", default_message) if processed_recipients else default_message
                
                if not bulk_message:
                    results[platform] = {
                        "success": False,
                        "message": "No message provided for bulk sending",
                        "results": []
                    }
                    continue
                
                # Send bulk messages
                platform_result = bulk_handler(processed_recipients, bulk_message)
                results[platform] = platform_result
                
                # Update totals
                total_sent += platform_result.get("successful_sends", 0)
                total_failed += platform_result.get("failed_sends", 0)
                
                logger.info(f"Completed {platform}: {platform_result.get('successful_sends', 0)} sent, {platform_result.get('failed_sends', 0)} failed")
                
            except Exception as e:
                error_msg = f"Error processing {platform}: {str(e)}"
                logger.error(error_msg)
                results[platform] = {
                    "success": False,
                    "message": error_msg,
                    "results": []
                }
        
        logger.info(f"Bulk campaign completed: {total_sent} sent, {total_failed} failed")
        
        return {
            "success": True,
            "total_sent": total_sent,
            "total_failed": total_failed,
            "platform_results": results,
            "summary": {
                "platforms_processed": len(campaign_data),
                "total_recipients": sum(len(recipients) for recipients in campaign_data.values()),
                "success_rate": (total_sent / (total_sent + total_failed) * 100) if (total_sent + total_failed) > 0 else 0
            }
        }
    
    def get_platform_status(self, platform: str = None) -> Dict[str, Any]:
        """
        Check if platform(s) are connected and ready
        Args:
            platform: Specific platform to check, or None for all platforms
        """
        if platform:
            if platform not in self.platforms:
                return {
                    "success": False,
                    "message": f"Platform '{platform}' not supported"
                }
            
            platform_obj = self.platforms[platform]
            
            # Check authentication status based on platform
            if hasattr(platform_obj, 'check_authentication_status'):
                status = platform_obj.check_authentication_status()
            elif hasattr(platform_obj, 'is_authenticated'):
                status = {
                    "authenticated": platform_obj.is_authenticated,
                    "message": "Connected" if platform_obj.is_authenticated else "Not connected"
                }
            else:
                status = {
                    "authenticated": False,
                    "message": "Status check not available"
                }
            
            return {
                "success": True,
                "platform": platform,
                "status": status
            }
        else:
            # Check all platforms
            all_status = {}
            for platform_name, platform_obj in self.platforms.items():
                try:
                    if hasattr(platform_obj, 'check_authentication_status'):
                        status = platform_obj.check_authentication_status()
                    elif hasattr(platform_obj, 'is_authenticated'):
                        status = {
                            "authenticated": platform_obj.is_authenticated,
                            "message": "Connected" if platform_obj.is_authenticated else "Not connected"
                        }
                    else:
                        status = {
                            "authenticated": False,
                            "message": "Status check not available"
                        }
                    
                    all_status[platform_name] = status
                    
                except Exception as e:
                    all_status[platform_name] = {
                        "authenticated": False,
                        "message": f"Error checking status: {str(e)}"
                    }
            
            return {
                "success": True,
                "all_platforms": all_status
            }
    
    def authenticate_platform(self, platform: str) -> Dict[str, Any]:
        """
        Authenticate a specific platform
        Args:
            platform: Platform name to authenticate
        """
        if platform not in self.platforms:
            return {
                "success": False,
                "message": f"Platform '{platform}' not supported"
            }
        
        try:
            platform_obj = self.platforms[platform]
            
            if hasattr(platform_obj, 'authenticate_account'):
                result = platform_obj.authenticate_account()
                logger.info(f"Authentication attempt for {platform}: {result.get('message')}")
                return result
            else:
                return {
                    "success": False,
                    "message": f"Authentication not available for {platform}"
                }
                
        except Exception as e:
            error_msg = f"Error authenticating {platform}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }
    
    def get_supported_platforms(self) -> List[str]:
        """Get list of supported platforms"""
        return list(self.platforms.keys())
    
    def get_platform_capabilities(self, platform: str) -> Dict[str, Any]:
        """Get capabilities and limitations for a specific platform"""
        capabilities = {
            'whatsapp': {
                "direct_messaging": True,
                "bulk_messaging": True,
                "media_support": True,
                "authentication": "QR Code",
                "limitations": "Requires WhatsApp Web authentication"
            },
            'telegram': {
                "direct_messaging": True,
                "bulk_messaging": True,
                "group_messaging": True,
                "media_support": True,
                "authentication": "Bot Token",
                "limitations": "Users must start conversation with bot first"
            },
            'facebook': {
                "direct_messaging": True,
                "bulk_messaging": True,
                "page_messaging": True,
                "authentication": "OAuth/Access Token",
                "limitations": "Requires Facebook page and proper permissions"
            },
            'instagram': {
                "direct_messaging": "Limited",
                "comment_replies": True,
                "story_interactions": True,
                "authentication": "OAuth/Unipile",
                "limitations": "Very restricted messaging, mainly comment-based"
            },
            'tiktok': {
                "direct_messaging": "Limited",
                "comment_replies": True,
                "authentication": "OAuth/Unipile",
                "limitations": "No direct messaging API, mainly comment interactions"
            },
            'linkedin': {
                "connection_requests": True,
                "inmail": "Premium",
                "direct_messaging": "Limited",
                "authentication": "OAuth/Unipile",
                "limitations": "Strict rate limits, requires connections for messaging"
            }
        }
        
        if platform in capabilities:
            return {
                "success": True,
                "platform": platform,
                "capabilities": capabilities[platform]
            }
        else:
            return {
                "success": False,
                "message": f"Platform '{platform}' not supported"
            }
