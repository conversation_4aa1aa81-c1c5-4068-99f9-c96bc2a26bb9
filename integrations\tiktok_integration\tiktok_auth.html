<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #000000, #ff0050, #00f2ea);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff0050, #00f2ea);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .warning-box h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .auth-methods {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }
        
        .auth-method {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }
        
        .auth-method h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .connect-btn {
            background: #ff0050;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .connect-btn:hover {
            background: #e6004a;
            transform: translateY(-2px);
        }
        
        .connect-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">🎵</div>
        <h1>TikTok Authentication</h1>
        <p class="subtitle">Connect your TikTok account for messaging and interaction capabilities</p>
        
        <div class="warning-box">
            <h3>⚠️ Important Limitations</h3>
            <ul>
                <li>TikTok has very limited messaging capabilities</li>
                <li>Direct messaging is only available through Unipile</li>
                <li>Most interactions are limited to comment replies</li>
                <li>Business API access requires approval from TikTok</li>
            </ul>
        </div>
        
        <div class="auth-methods">
            <!-- Unipile Method -->
            <div class="auth-method">
                <h3>Method 1: Unipile Integration (Recommended)</h3>
                <p>Use Unipile for TikTok messaging capabilities</p>
                
                <button class="connect-btn" id="unipile-btn" onclick="authenticateUnipile()">
                    Connect via Unipile
                </button>
            </div>
            
            <!-- Business API Method -->
            <div class="auth-method">
                <h3>Method 2: TikTok Business API</h3>
                <p>Connect using TikTok for Business API credentials</p>
                
                <div class="form-group">
                    <label for="access-token">Access Token:</label>
                    <input type="text" id="access-token" placeholder="Enter your TikTok access token">
                </div>
                
                <div class="form-group">
                    <label for="app-id">App ID (Optional):</label>
                    <input type="text" id="app-id" placeholder="Enter your TikTok App ID">
                </div>
                
                <div class="form-group">
                    <label for="app-secret">App Secret (Optional):</label>
                    <input type="password" id="app-secret" placeholder="Enter your TikTok App Secret">
                </div>
                
                <button class="connect-btn" id="business-btn" onclick="setupBusinessAccount()">
                    Setup Business Account
                </button>
                <button class="connect-btn" id="test-btn" onclick="testConnection()" style="display: none;">
                    Test Connection
                </button>
            </div>
        </div>
        
        <div class="status-indicator" id="status-indicator"></div>
        
        <div class="instructions">
            <h3>Setup Instructions:</h3>
            
            <h4>For Unipile Integration:</h4>
            <ol>
                <li>Click "Connect via Unipile" above</li>
                <li>Follow the authentication flow</li>
                <li>Grant necessary permissions</li>
                <li>Start interacting through Unipile</li>
            </ol>
            
            <h4>For Business API Setup:</h4>
            <ol>
                <li>Apply for TikTok for Business API access</li>
                <li>Create a TikTok for Business app</li>
                <li>Get approval from TikTok (this can take time)</li>
                <li>Obtain your access token from the developer portal</li>
                <li>Enter credentials above and click "Setup Business Account"</li>
            </ol>
            
            <h4>Available Features:</h4>
            <ul>
                <li><strong>Via Unipile:</strong> Limited direct messaging</li>
                <li><strong>Via Business API:</strong> Comment management, user info</li>
                <li><strong>Both:</strong> Account information, basic interactions</li>
            </ul>
            
            <h4>Important Notes:</h4>
            <ul>
                <li>TikTok has the most restrictive messaging policies</li>
                <li>Business API access requires approval and compliance review</li>
                <li>Most messaging features are not available through public APIs</li>
                <li>Focus on comment interactions and engagement rather than direct messaging</li>
                <li>Consider using TikTok for brand awareness rather than direct outreach</li>
            </ul>
        </div>
    </div>

    <script>
        // API base URL - adjust as needed
        const API_BASE = '/api/tiktok';
        
        async function authenticateUnipile() {
            const unipileBtn = document.getElementById('unipile-btn');
            
            unipileBtn.disabled = true;
            unipileBtn.textContent = 'Connecting...';
            showStatus('waiting', 'Authenticating via Unipile...');
            
            try {
                const response = await fetch(`${API_BASE}/authenticate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    if (data.auth_url) {
                        showStatus('waiting', 'Redirecting to authentication...');
                        window.open(data.auth_url, '_blank');
                    } else {
                        showStatus('success', 'TikTok account authenticated via Unipile!');
                    }
                } else {
                    showStatus('error', data.message || 'Failed to authenticate via Unipile');
                }
                
            } catch (error) {
                console.error('Unipile auth error:', error);
                showStatus('error', 'Failed to connect via Unipile');
            }
            
            unipileBtn.disabled = false;
            unipileBtn.textContent = 'Connect via Unipile';
        }
        
        async function setupBusinessAccount() {
            const accessToken = document.getElementById('access-token').value.trim();
            const appId = document.getElementById('app-id').value.trim();
            const appSecret = document.getElementById('app-secret').value.trim();
            const businessBtn = document.getElementById('business-btn');
            const testBtn = document.getElementById('test-btn');
            
            if (!accessToken) {
                showStatus('error', 'Please enter an access token');
                return;
            }
            
            businessBtn.disabled = true;
            businessBtn.textContent = 'Setting up...';
            showStatus('waiting', 'Configuring TikTok Business account...');
            
            try {
                const requestBody = {
                    access_token: accessToken
                };
                
                if (appId) requestBody.app_id = appId;
                if (appSecret) requestBody.app_secret = appSecret;
                
                const response = await fetch(`${API_BASE}/setup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'TikTok Business account configured successfully!');
                    testBtn.style.display = 'inline-block';
                    
                    // Store credentials for convenience
                    localStorage.setItem('tiktok_access_token', accessToken);
                    if (appId) localStorage.setItem('tiktok_app_id', appId);
                    
                } else {
                    showStatus('error', data.message || 'Failed to setup TikTok Business account');
                }
                
            } catch (error) {
                console.error('Setup error:', error);
                showStatus('error', 'Network error. Please try again.');
            }
            
            businessBtn.disabled = false;
            businessBtn.textContent = 'Setup Business Account';
        }
        
        async function testConnection() {
            const testBtn = document.getElementById('test-btn');
            const originalText = testBtn.textContent;
            
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';
            showStatus('waiting', 'Testing TikTok connection...');
            
            try {
                const response = await fetch(`${API_BASE}/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'TikTok connection is working correctly!');
                } else {
                    showStatus('error', data.message || 'Connection test failed');
                }
                
            } catch (error) {
                console.error('Test error:', error);
                showStatus('error', 'Failed to test connection');
            }
            
            testBtn.disabled = false;
            testBtn.textContent = originalText;
        }
        
        function showStatus(type, message) {
            const statusIndicator = document.getElementById('status-indicator');
            statusIndicator.className = `status-indicator status-${type}`;
            statusIndicator.textContent = message;
            statusIndicator.style.display = 'block';
        }
        
        // Load saved values on page load
        window.addEventListener('load', function() {
            const savedToken = localStorage.getItem('tiktok_access_token');
            const savedAppId = localStorage.getItem('tiktok_app_id');
            
            if (savedToken) {
                document.getElementById('access-token').value = savedToken;
            }
            if (savedAppId) {
                document.getElementById('app-id').value = savedAppId;
            }
        });
    </script>
</body>
</html>
