"""
Telegram Messaging Integration using Bot API and Unipile
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unipile_config import UnipileClient
import json
import requests
import asyncio
from typing import List, Dict, Any, Optional
import time


class TelegramMessaging:
    """Telegram messaging class with Bot API and Unipile integration"""
    
    def __init__(self, bot_token: str = None, unipile_api_key: str = None):
        """Initialize Telegram messaging"""
        if unipile_api_key is None:
            unipile_api_key = "b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s="
        
        self.bot_token = bot_token
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.is_authenticated = False
        self.bot_info = None
        
        # Telegram Bot API base URL
        if bot_token:
            self.bot_api_url = f"https://api.telegram.org/bot{bot_token}"
        
        # Load configuration
        self.config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        self.load_config()
    
    def load_config(self):
        """Load Telegram configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                if self.config.get("bot_token") and not self.bot_token:
                    self.bot_token = self.config["bot_token"]
                    self.bot_api_url = f"https://api.telegram.org/bot{self.bot_token}"
        except FileNotFoundError:
            self.config = {
                "bot_token": None,
                "account_id": None,
                "bot_username": None,
                "last_authenticated": None
            }
            self.save_config()
    
    def save_config(self):
        """Save Telegram configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def setup_bot(self, bot_token: str = None) -> Dict[str, Any]:
        """
        Configure Telegram bot with token
        Args:
            bot_token: Telegram bot token from BotFather
        """
        if bot_token:
            self.bot_token = bot_token
            self.bot_api_url = f"https://api.telegram.org/bot{bot_token}"
            self.config["bot_token"] = bot_token
        
        if not self.bot_token:
            return {
                "success": False,
                "message": "Bot token is required"
            }
        
        try:
            # Test bot token by getting bot info
            response = requests.get(f"{self.bot_api_url}/getMe")
            data = response.json()
            
            if data.get("ok"):
                self.bot_info = data["result"]
                self.is_authenticated = True
                self.config["bot_username"] = self.bot_info.get("username")
                self.config["last_authenticated"] = time.time()
                self.save_config()
                
                return {
                    "success": True,
                    "message": "Bot configured successfully",
                    "bot_info": self.bot_info
                }
            else:
                return {
                    "success": False,
                    "message": f"Invalid bot token: {data.get('description', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Bot setup error: {str(e)}"
            }
    
    def authenticate_account(self) -> Dict[str, Any]:
        """Authenticate Telegram account via Unipile"""
        try:
            auth_response = self.unipile.authenticate_account("telegram")
            
            if auth_response.get("success"):
                if "account_id" in auth_response:
                    self.account_id = auth_response["account_id"]
                    self.config["account_id"] = self.account_id
                    self.save_config()
                
                return {
                    "success": True,
                    "message": "Telegram account authenticated via Unipile",
                    "account_id": self.account_id,
                    "auth_data": auth_response
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to authenticate Telegram account",
                    "error": auth_response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Authentication error: {str(e)}"
            }
    
    def send_message(self, user_id: str, message: str, parse_mode: str = "HTML") -> Dict[str, Any]:
        """
        Send direct message to user via Bot API
        Args:
            user_id: Telegram user ID or username
            message: Message text to send
            parse_mode: Message formatting (HTML, Markdown, or None)
        """
        if not self.is_authenticated and not self.bot_token:
            return {
                "success": False,
                "message": "Bot not configured. Please setup bot token first."
            }
        
        try:
            # Prepare message data
            data = {
                "chat_id": user_id,
                "text": message
            }
            
            if parse_mode:
                data["parse_mode"] = parse_mode
            
            # Send via Bot API
            response = requests.post(f"{self.bot_api_url}/sendMessage", json=data)
            result = response.json()
            
            if result.get("ok"):
                return {
                    "success": True,
                    "message": "Message sent successfully",
                    "message_id": result["result"]["message_id"],
                    "recipient": user_id
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to send message: {result.get('description', 'Unknown error')}",
                    "error_code": result.get("error_code")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Send message error: {str(e)}"
            }
    
    def send_message_unipile(self, user_id: str, message: str) -> Dict[str, Any]:
        """Send message via Unipile API"""
        if not self.account_id:
            return {
                "success": False,
                "message": "Unipile account not authenticated"
            }
        
        try:
            response = self.unipile.send_message(
                account_id=self.account_id,
                recipient=user_id,
                message=message
            )
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": "Message sent via Unipile",
                    "message_id": response.get("message_id"),
                    "recipient": user_id
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send message via Unipile",
                    "error": response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Unipile send error: {str(e)}"
            }
    
    def send_group_message(self, group_id: str, message: str, parse_mode: str = "HTML") -> Dict[str, Any]:
        """
        Send message to Telegram group
        Args:
            group_id: Telegram group ID or username
            message: Message text to send
            parse_mode: Message formatting
        """
        return self.send_message(group_id, message, parse_mode)
    
    def send_bulk_messages(self, recipients: List[Dict[str, str]], message: str) -> Dict[str, Any]:
        """
        Send bulk messages to multiple recipients
        Args:
            recipients: List of recipient data [{"user_id": "@username", "name": "John"}]
            message: Message text to send
        """
        if not self.is_authenticated and not self.bot_token:
            return {
                "success": False,
                "message": "Bot not configured"
            }
        
        results = []
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            user_id = recipient.get("user_id") or recipient.get("chat_id")
            if not user_id:
                results.append({
                    "recipient": recipient,
                    "success": False,
                    "message": "No user ID provided"
                })
                failed_sends += 1
                continue
            
            # Personalize message if name is provided
            personalized_message = message
            if recipient.get("name"):
                personalized_message = f"Hi {recipient['name']}, {message}"
            
            # Send individual message
            send_result = self.send_message(user_id, personalized_message)
            results.append({
                "recipient": recipient,
                "success": send_result["success"],
                "message": send_result["message"],
                "message_id": send_result.get("message_id")
            })
            
            if send_result["success"]:
                successful_sends += 1
            else:
                failed_sends += 1
            
            # Add delay to avoid rate limiting
            time.sleep(0.5)
        
        return {
            "success": True,
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "results": results
        }
    
    def get_bot_info(self) -> Dict[str, Any]:
        """Get information about the configured bot"""
        if not self.bot_token:
            return {
                "success": False,
                "message": "No bot token configured"
            }
        
        try:
            response = requests.get(f"{self.bot_api_url}/getMe")
            data = response.json()
            
            if data.get("ok"):
                return {
                    "success": True,
                    "bot_info": data["result"]
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to get bot info: {data.get('description', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting bot info: {str(e)}"
            }
    
    def get_updates(self, offset: int = None, limit: int = 100) -> Dict[str, Any]:
        """Get updates from Telegram (for webhook alternative)"""
        if not self.bot_token:
            return {
                "success": False,
                "message": "No bot token configured"
            }
        
        try:
            params = {"limit": limit}
            if offset:
                params["offset"] = offset
            
            response = requests.get(f"{self.bot_api_url}/getUpdates", params=params)
            data = response.json()
            
            if data.get("ok"):
                return {
                    "success": True,
                    "updates": data["result"]
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to get updates: {data.get('description', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting updates: {str(e)}"
            }
