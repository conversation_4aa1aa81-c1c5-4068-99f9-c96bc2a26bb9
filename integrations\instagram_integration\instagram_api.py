"""
Instagram Messaging Integration using Instagram Basic Display API and Unipile
Note: Instagram has strict messaging limitations - focus on comments and story interactions
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unipile_config import UnipileClient
import json
import requests
from typing import List, Dict, Any, Optional
import time
import urllib.parse


class InstagramMessaging:
    """Instagram messaging class with Basic Display API and Unipile integration"""
    
    def __init__(self, access_token: str = None, unipile_api_key: str = None):
        """Initialize Instagram messaging"""
        if unipile_api_key is None:
            unipile_api_key = "b6hDoV4N.fi7MF5SvQ1g7tH8CEr6TpExQHoXbV8tKH/qQseSKN5s="
        
        self.access_token = access_token
        self.unipile = UnipileClient(unipile_api_key)
        self.account_id = None
        self.instagram_business_account_id = None
        self.is_authenticated = False
        
        # Instagram Graph API base URL
        self.graph_api_url = "https://graph.facebook.com/v18.0"
        
        # Load configuration
        self.config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        self.load_config()
    
    def load_config(self):
        """Load Instagram configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                if self.config.get("access_token") and not self.access_token:
                    self.access_token = self.config["access_token"]
        except FileNotFoundError:
            self.config = {
                "access_token": null,
                "account_id": null,
                "instagram_business_account_id": null,
                "username": null,
                "last_authenticated": null
            }
            self.save_config()
    
    def save_config(self):
        """Save Instagram configuration"""
        with open(self.config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def authenticate_account(self) -> Dict[str, Any]:
        """Authenticate Instagram account via Unipile"""
        try:
            auth_response = self.unipile.authenticate_account("instagram")
            
            if auth_response.get("success"):
                if "account_id" in auth_response:
                    self.account_id = auth_response["account_id"]
                    self.config["account_id"] = self.account_id
                    self.save_config()
                
                return {
                    "success": True,
                    "message": "Instagram account authenticated via Unipile",
                    "account_id": self.account_id,
                    "auth_url": auth_response.get("auth_url"),
                    "auth_data": auth_response
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to authenticate Instagram account",
                    "error": auth_response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Authentication error: {str(e)}"
            }
    
    def setup_business_account(self, access_token: str) -> Dict[str, Any]:
        """
        Setup Instagram Business account access
        Args:
            access_token: Instagram access token (usually from Facebook)
        """
        self.access_token = access_token
        self.config["access_token"] = access_token
        
        try:
            # Get Instagram Business Account ID
            response = requests.get(
                f"{self.graph_api_url}/me/accounts",
                params={
                    "access_token": access_token,
                    "fields": "instagram_business_account"
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                accounts = data.get("data", [])
                
                for account in accounts:
                    if account.get("instagram_business_account"):
                        self.instagram_business_account_id = account["instagram_business_account"]["id"]
                        self.config["instagram_business_account_id"] = self.instagram_business_account_id
                        break
                
                if self.instagram_business_account_id:
                    # Get account info
                    account_info = self.get_account_info()
                    if account_info.get("success"):
                        self.config["username"] = account_info["account_info"].get("username")
                        self.config["last_authenticated"] = time.time()
                        self.is_authenticated = True
                        self.save_config()
                        
                        return {
                            "success": True,
                            "message": "Instagram Business account configured",
                            "account_info": account_info["account_info"]
                        }
                    else:
                        return account_info
                else:
                    return {
                        "success": False,
                        "message": "No Instagram Business account found. Please connect an Instagram Business account to your Facebook page."
                    }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Failed to get accounts: {error_data.get('error', {}).get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Account setup error: {str(e)}"
            }
    
    def send_message(self, recipient_id: str, message: str) -> Dict[str, Any]:
        """
        Send direct message via Instagram (Limited functionality)
        Note: Instagram messaging is very restricted. This is primarily for Unipile integration.
        """
        # Instagram Graph API doesn't support direct messaging for most use cases
        # Try Unipile first
        if self.account_id:
            return self.send_message_unipile(recipient_id, message)
        else:
            return {
                "success": False,
                "message": "Instagram direct messaging requires Unipile integration or Instagram Messaging API access"
            }
    
    def send_message_unipile(self, recipient_id: str, message: str) -> Dict[str, Any]:
        """Send message via Unipile API"""
        if not self.account_id:
            return {
                "success": False,
                "message": "Unipile account not authenticated"
            }
        
        try:
            response = self.unipile.send_message(
                account_id=self.account_id,
                recipient=recipient_id,
                message=message
            )
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": "Message sent via Unipile",
                    "message_id": response.get("message_id"),
                    "recipient": recipient_id
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send message via Unipile",
                    "error": response.get("error", "Unknown error")
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Unipile send error: {str(e)}"
            }
    
    def reply_to_comment(self, comment_id: str, reply_text: str) -> Dict[str, Any]:
        """
        Reply to a comment on Instagram post
        Args:
            comment_id: Instagram comment ID
            reply_text: Reply text
        """
        if not self.access_token or not self.instagram_business_account_id:
            return {
                "success": False,
                "message": "Instagram Business account not configured"
            }
        
        try:
            response = requests.post(
                f"{self.graph_api_url}/{comment_id}/replies",
                params={
                    "access_token": self.access_token,
                    "message": reply_text
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "Comment reply sent successfully",
                    "reply_id": result.get("id")
                }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Failed to reply to comment: {error_data.get('error', {}).get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Comment reply error: {str(e)}"
            }
    
    def send_bulk_messages(self, recipients: List[Dict[str, str]], message: str) -> Dict[str, Any]:
        """
        Send bulk messages (primarily via Unipile)
        Args:
            recipients: List of recipient data
            message: Message text to send
        """
        if not self.account_id:
            return {
                "success": False,
                "message": "Instagram messaging requires Unipile authentication"
            }
        
        results = []
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            user_id = recipient.get("user_id") or recipient.get("username")
            if not user_id:
                results.append({
                    "recipient": recipient,
                    "success": False,
                    "message": "No user ID or username provided"
                })
                failed_sends += 1
                continue
            
            # Personalize message if name is provided
            personalized_message = message
            if recipient.get("name"):
                personalized_message = f"Hi {recipient['name']}, {message}"
            
            # Send via Unipile
            send_result = self.send_message_unipile(user_id, personalized_message)
            
            results.append({
                "recipient": recipient,
                "success": send_result["success"],
                "message": send_result["message"],
                "message_id": send_result.get("message_id")
            })
            
            if send_result["success"]:
                successful_sends += 1
            else:
                failed_sends += 1
            
            # Add delay to avoid rate limiting
            time.sleep(2)  # Instagram has stricter rate limits
        
        return {
            "success": True,
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "results": results
        }
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get Instagram Business account information"""
        if not self.access_token or not self.instagram_business_account_id:
            return {
                "success": False,
                "message": "Instagram Business account not configured"
            }
        
        try:
            response = requests.get(
                f"{self.graph_api_url}/{self.instagram_business_account_id}",
                params={
                    "access_token": self.access_token,
                    "fields": "id,username,account_type,media_count,followers_count"
                }
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "account_info": response.json()
                }
            else:
                error_data = response.json()
                return {
                    "success": False,
                    "message": f"Failed to get account info: {error_data.get('error', {}).get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting account info: {str(e)}"
            }
    
    def get_recent_comments(self, limit: int = 25) -> Dict[str, Any]:
        """Get recent comments on Instagram posts"""
        if not self.access_token or not self.instagram_business_account_id:
            return {
                "success": False,
                "message": "Instagram Business account not configured"
            }
        
        try:
            # First get recent media
            media_response = requests.get(
                f"{self.graph_api_url}/{self.instagram_business_account_id}/media",
                params={
                    "access_token": self.access_token,
                    "fields": "id,caption,comments_count",
                    "limit": 10
                }
            )
            
            if media_response.status_code != 200:
                return {
                    "success": False,
                    "message": "Failed to get recent media"
                }
            
            media_data = media_response.json()
            all_comments = []
            
            # Get comments for each media item
            for media in media_data.get("data", []):
                if media.get("comments_count", 0) > 0:
                    comments_response = requests.get(
                        f"{self.graph_api_url}/{media['id']}/comments",
                        params={
                            "access_token": self.access_token,
                            "fields": "id,text,username,timestamp",
                            "limit": limit
                        }
                    )
                    
                    if comments_response.status_code == 200:
                        comments_data = comments_response.json()
                        for comment in comments_data.get("data", []):
                            comment["media_id"] = media["id"]
                            all_comments.append(comment)
            
            return {
                "success": True,
                "comments": all_comments[:limit]
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Error getting comments: {str(e)}"
            }
    
    def generate_oauth_url(self, app_id: str, redirect_uri: str, state: str = None) -> str:
        """Generate OAuth URL for Instagram authentication"""
        base_url = "https://api.instagram.com/oauth/authorize"
        
        params = {
            "client_id": app_id,
            "redirect_uri": redirect_uri,
            "scope": "user_profile,user_media",
            "response_type": "code"
        }
        
        if state:
            params["state"] = state
        
        return f"{base_url}?{urllib.parse.urlencode(params)}"
