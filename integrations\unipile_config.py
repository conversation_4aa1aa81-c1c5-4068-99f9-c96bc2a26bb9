"""
Unipile API Configuration and Client Setup
"""
import requests
import json
import os
from typing import Dict, Any, Optional


class UnipileClient:
    """Unipile API client for social media integrations"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        # Updated to use the correct Unipile API format
        # Note: In production, you should use your actual DSN from the Unipile dashboard
        self.base_url = "https://api1.unipile.com:13115/api/v1"
        self.headers = {
            "X-API-KEY": api_key,  # Unipile uses X-API-KEY header
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def authenticate_account(self, provider: str) -> Dict[str, Any]:
        """Authenticate account for a specific provider"""
        url = f"{self.base_url}/accounts"
        # Use the correct provider format for Unipile API
        provider_map = {
            "whatsapp": "WHATSAPP",
            "telegram": "TELEGRAM",
            "facebook": "FACEBOOK",
            "instagram": "INSTAGRAM",
            "linkedin": "LINKEDIN",
            "tiktok": "TIKTOK"
        }

        data = {"provider": provider_map.get(provider.lower(), provider.upper())}

        try:
            response = requests.post(url, headers=self.headers, json=data)
            response.raise_for_status()  # Raise an exception for bad status codes
            result = response.json()

            # Check if the response contains the expected data
            if "object" in result:
                if result["object"] == "Account":
                    # Standard account response
                    return {
                        "success": True,
                        "account_id": result.get("id"),
                        "status": result.get("status"),
                        "provider": result.get("provider"),
                        "auth_url": result.get("auth_url")
                    }
                elif result["object"] == "Checkpoint":
                    # WhatsApp QR code authentication response
                    checkpoint = result.get("checkpoint", {})
                    if checkpoint.get("type") == "QRCODE" and "qrcode" in checkpoint:
                        return {
                            "success": True,
                            "qr_code": checkpoint["qrcode"],
                            "account_id": result.get("account_id"),
                            "checkpoint_type": checkpoint.get("type"),
                            "provider": provider.upper()
                        }
                    else:
                        return {
                            "success": True,
                            "checkpoint": checkpoint,
                            "account_id": result.get("account_id"),
                            "provider": provider.upper()
                        }
                else:
                    # Unknown object type, return as-is
                    return result
            else:
                return result

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"Request failed: {str(e)}",
                "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                "response_text": getattr(e.response, 'text', None) if hasattr(e, 'response') else None
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}"
            }
    
    def get_accounts(self) -> Dict[str, Any]:
        """Get all authenticated accounts"""
        url = f"{self.base_url}/accounts"
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def send_message(self, account_id: str, recipient: str, message: str, 
                    message_type: str = "text") -> Dict[str, Any]:
        """Send message through Unipile API"""
        url = f"{self.base_url}/chats"
        
        # Use multipart/form-data format as preferred
        files = {
            'attendees_ids': (None, recipient),
            'text': (None, message),
            'account_id': (None, account_id)
        }
        
        headers = {"X-API-KEY": self.api_key}
        response = requests.post(url, headers=headers, files=files)
        return response.json()
    
    def get_chat_history(self, account_id: str, chat_id: str) -> Dict[str, Any]:
        """Get chat history for a specific conversation"""
        url = f"{self.base_url}/chats/{chat_id}/messages"
        params = {"account_id": account_id}
        
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()
    
    def get_account_status(self, account_id: str) -> Dict[str, Any]:
        """Check account connection status"""
        url = f"{self.base_url}/accounts/{account_id}"
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            result = response.json()

            # Transform the response to match expected format
            if "object" in result and result["object"] == "Account":
                status = result.get("status", "").upper()
                is_connected = status == "CONNECTED"

                return {
                    "connected": is_connected,
                    "status": status,
                    "phone_number": result.get("username"),
                    "provider": result.get("provider"),
                    "account_id": result.get("id"),
                    "display_name": result.get("display_name"),
                    "profile_picture": result.get("profile_picture"),
                    "last_seen": result.get("last_seen"),
                    "connection_time": result.get("created_at") if is_connected else None
                }
            else:
                # Handle other response types (like Checkpoint)
                return {
                    "connected": False,
                    "status": "PENDING",
                    "message": "Account authentication in progress",
                    "raw_response": result
                }

        except requests.exceptions.RequestException as e:
            return {
                "connected": False,
                "error": f"Request failed: {str(e)}",
                "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
            }
        except Exception as e:
            return {
                "connected": False,
                "error": f"Unexpected error: {str(e)}"
            }


class UnipileConfig:
    """Configuration class for Unipile integration"""

    def __init__(self):
        self.config_path = os.path.join(os.path.dirname(__file__), 'api_config.json')
        self.load_config()

    def load_config(self):
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)

            self.api_key = config["unipile"]["api_key"]
            self.base_url = config["unipile"]["base_url"]
            self.platforms = config["platforms"]

        except FileNotFoundError:
            # Fallback to default configuration with working API key
            self.api_key = "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="
            self.base_url = "https://api1.unipile.com:13115/api/v1"
            self.platforms = {
                "whatsapp": {"provider": "WHATSAPP", "auth_type": "qr_code"},
                "telegram": {"provider": "TELEGRAM", "auth_type": "bot_token"},
                "facebook": {"provider": "FACEBOOK", "auth_type": "oauth"},
                "instagram": {"provider": "INSTAGRAM", "auth_type": "oauth"},
                "tiktok": {"provider": "TIKTOK", "auth_type": "oauth"},
                "linkedin": {"provider": "LINKEDIN", "auth_type": "oauth"}
            }
        except Exception as e:
            print(f"Error loading config: {e}")
            # Use fallback configuration with working API key
            self.api_key = "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="
            self.base_url = "https://api1.unipile.com:13115/api/v1"
            self.platforms = {
                "whatsapp": {"provider": "WHATSAPP", "auth_type": "qr_code"}
            }
    
    def get_client(self) -> UnipileClient:
        """Get configured Unipile client"""
        return UnipileClient(self.api_key)
    
    def get_platform_config(self, platform: str) -> Optional[Dict[str, Any]]:
        """Get configuration for specific platform"""
        return self.platforms.get(platform)
