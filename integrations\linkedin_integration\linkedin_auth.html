<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0077b5, #00a0dc);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #0077b5;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .warning-box h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .auth-methods {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }
        
        .auth-method {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }
        
        .auth-method h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .connect-btn {
            background: #0077b5;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .connect-btn:hover {
            background: #005885;
            transform: translateY(-2px);
        }
        
        .connect-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .oauth-btn {
            background: #00a0dc;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .oauth-btn:hover {
            background: #0077b5;
            transform: translateY(-2px);
        }
        
        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .profile-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            display: none;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">💼</div>
        <h1>LinkedIn Authentication</h1>
        <p class="subtitle">Connect your LinkedIn account for professional messaging and networking</p>
        
        <div class="warning-box">
            <h3>⚠️ Important Limitations</h3>
            <ul>
                <li>LinkedIn has strict messaging policies and rate limits</li>
                <li>Direct messaging requires existing connections</li>
                <li>InMail requires LinkedIn Premium or Sales Navigator</li>
                <li>API access requires LinkedIn partnership approval</li>
            </ul>
        </div>
        
        <div class="auth-methods">
            <!-- Unipile Method -->
            <div class="auth-method">
                <h3>Method 1: Unipile Integration (Recommended)</h3>
                <p>Use Unipile for LinkedIn messaging capabilities</p>
                
                <button class="connect-btn" id="unipile-btn" onclick="authenticateUnipile()">
                    Connect via Unipile
                </button>
            </div>
            
            <!-- OAuth Method -->
            <div class="auth-method">
                <h3>Method 2: OAuth Authentication</h3>
                <p>Use LinkedIn's OAuth flow for API access</p>
                
                <div class="form-group">
                    <label for="client-id">LinkedIn Client ID:</label>
                    <input type="text" id="client-id" placeholder="Enter your LinkedIn App Client ID">
                </div>
                
                <button class="oauth-btn" id="oauth-btn" onclick="startOAuth()">
                    Connect with LinkedIn
                </button>
            </div>
            
            <!-- Direct Token Method -->
            <div class="auth-method">
                <h3>Method 3: Direct Access Token</h3>
                <p>Enter a LinkedIn access token directly</p>
                
                <div class="form-group">
                    <label for="access-token">Access Token:</label>
                    <input type="text" id="access-token" placeholder="Enter your LinkedIn access token">
                </div>
                
                <button class="connect-btn" id="token-btn" onclick="setupWithToken()">
                    Setup with Token
                </button>
                <button class="connect-btn" id="test-btn" onclick="testConnection()" style="display: none;">
                    Test Connection
                </button>
            </div>
        </div>
        
        <div class="status-indicator" id="status-indicator"></div>
        
        <div class="profile-info" id="profile-info">
            <h4>Connected Profile Information</h4>
            <p><strong>Name:</strong> <span id="profile-name">-</span></p>
            <p><strong>Email:</strong> <span id="profile-email">-</span></p>
            <p><strong>Headline:</strong> <span id="profile-headline">-</span></p>
            <p><strong>Industry:</strong> <span id="profile-industry">-</span></p>
        </div>
        
        <div class="instructions">
            <h3>Setup Instructions:</h3>
            
            <h4>For Unipile Integration:</h4>
            <ol>
                <li>Click "Connect via Unipile" above</li>
                <li>Follow the authentication flow</li>
                <li>Grant necessary permissions</li>
                <li>Start professional networking through Unipile</li>
            </ol>
            
            <h4>For OAuth Authentication:</h4>
            <ol>
                <li>Create a LinkedIn App at <a href="https://www.linkedin.com/developers/" target="_blank">LinkedIn Developers</a></li>
                <li>Configure OAuth 2.0 settings</li>
                <li>Get your Client ID from the app settings</li>
                <li>Enter the Client ID above and click "Connect with LinkedIn"</li>
                <li>Complete the OAuth flow to authorize access</li>
            </ol>
            
            <h4>For Direct Token:</h4>
            <ol>
                <li>Obtain an access token from LinkedIn's OAuth flow</li>
                <li>Ensure the token has required scopes: <span class="code">r_liteprofile</span>, <span class="code">r_emailaddress</span>, <span class="code">w_member_social</span></li>
                <li>Enter the token above and click "Setup with Token"</li>
            </ol>
            
            <h4>Available Features:</h4>
            <ul>
                <li><strong>Connection Requests:</strong> Send connection requests with messages</li>
                <li><strong>InMail:</strong> Send InMail messages (requires premium)</li>
                <li><strong>Profile Access:</strong> View and search professional profiles</li>
                <li><strong>Network Management:</strong> Manage professional connections</li>
            </ul>
            
            <h4>Best Practices:</h4>
            <ul>
                <li>Personalize connection requests and messages</li>
                <li>Respect LinkedIn's professional environment</li>
                <li>Follow LinkedIn's messaging policies and etiquette</li>
                <li>Use for professional networking, not spam</li>
                <li>Consider LinkedIn Sales Navigator for advanced features</li>
            </ul>
        </div>
    </div>

    <script>
        // API base URL - adjust as needed
        const API_BASE = '/api/linkedin';
        
        async function authenticateUnipile() {
            const unipileBtn = document.getElementById('unipile-btn');
            
            unipileBtn.disabled = true;
            unipileBtn.textContent = 'Connecting...';
            showStatus('waiting', 'Authenticating via Unipile...');
            
            try {
                const response = await fetch(`${API_BASE}/authenticate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    if (data.auth_url) {
                        showStatus('waiting', 'Redirecting to authentication...');
                        window.open(data.auth_url, '_blank');
                    } else {
                        showStatus('success', 'LinkedIn account authenticated via Unipile!');
                    }
                } else {
                    showStatus('error', data.message || 'Failed to authenticate via Unipile');
                }
                
            } catch (error) {
                console.error('Unipile auth error:', error);
                showStatus('error', 'Failed to connect via Unipile');
            }
            
            unipileBtn.disabled = false;
            unipileBtn.textContent = 'Connect via Unipile';
        }
        
        async function startOAuth() {
            const clientId = document.getElementById('client-id').value.trim();
            
            if (!clientId) {
                showStatus('error', 'Please enter your LinkedIn Client ID');
                return;
            }
            
            showStatus('waiting', 'Redirecting to LinkedIn...');
            
            try {
                // Generate OAuth URL
                const redirectUri = encodeURIComponent(window.location.origin + '/linkedin-callback');
                const state = Math.random().toString(36).substring(7);
                
                // Store state for verification
                localStorage.setItem('linkedin_oauth_state', state);
                localStorage.setItem('linkedin_client_id', clientId);
                
                const oauthUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=r_liteprofile%20r_emailaddress%20w_member_social&state=${state}`;
                
                // Redirect to LinkedIn OAuth
                window.location.href = oauthUrl;
                
            } catch (error) {
                console.error('OAuth error:', error);
                showStatus('error', 'Failed to start OAuth flow');
            }
        }
        
        async function setupWithToken() {
            const accessToken = document.getElementById('access-token').value.trim();
            const tokenBtn = document.getElementById('token-btn');
            const testBtn = document.getElementById('test-btn');
            
            if (!accessToken) {
                showStatus('error', 'Please enter an access token');
                return;
            }
            
            tokenBtn.disabled = true;
            tokenBtn.textContent = 'Setting up...';
            showStatus('waiting', 'Configuring LinkedIn API access...');
            
            try {
                const response = await fetch(`${API_BASE}/setup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        access_token: accessToken
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'LinkedIn API access configured successfully!');
                    showProfileInfo(data.profile_data);
                    testBtn.style.display = 'inline-block';
                    
                    // Store token for convenience
                    localStorage.setItem('linkedin_access_token', accessToken);
                    
                } else {
                    showStatus('error', data.message || 'Failed to setup LinkedIn API access');
                }
                
            } catch (error) {
                console.error('Setup error:', error);
                showStatus('error', 'Network error. Please try again.');
            }
            
            tokenBtn.disabled = false;
            tokenBtn.textContent = 'Setup with Token';
        }
        
        async function testConnection() {
            const testBtn = document.getElementById('test-btn');
            const originalText = testBtn.textContent;
            
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';
            showStatus('waiting', 'Testing LinkedIn connection...');
            
            try {
                const response = await fetch(`${API_BASE}/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', 'LinkedIn connection is working correctly!');
                    if (data.profile_info) {
                        showProfileInfo(data.profile_info);
                    }
                } else {
                    showStatus('error', data.message || 'Connection test failed');
                }
                
            } catch (error) {
                console.error('Test error:', error);
                showStatus('error', 'Failed to test connection');
            }
            
            testBtn.disabled = false;
            testBtn.textContent = originalText;
        }
        
        function showStatus(type, message) {
            const statusIndicator = document.getElementById('status-indicator');
            statusIndicator.className = `status-indicator status-${type}`;
            statusIndicator.textContent = message;
            statusIndicator.style.display = 'block';
        }
        
        function showProfileInfo(profileData) {
            const profileInfo = document.getElementById('profile-info');
            
            const firstName = profileData.firstName?.localized?.en_US || '';
            const lastName = profileData.lastName?.localized?.en_US || '';
            const fullName = `${firstName} ${lastName}`.trim() || '-';
            
            document.getElementById('profile-name').textContent = fullName;
            document.getElementById('profile-email').textContent = profileData.emailAddress || '-';
            document.getElementById('profile-headline').textContent = profileData.headline?.localized?.en_US || '-';
            document.getElementById('profile-industry').textContent = profileData.industry || '-';
            
            profileInfo.style.display = 'block';
        }
        
        // Load saved values on page load
        window.addEventListener('load', function() {
            const savedToken = localStorage.getItem('linkedin_access_token');
            const savedClientId = localStorage.getItem('linkedin_client_id');
            
            if (savedToken) {
                document.getElementById('access-token').value = savedToken;
            }
            if (savedClientId) {
                document.getElementById('client-id').value = savedClientId;
            }
            
            // Check if this is an OAuth callback
            if (window.location.search.includes('code=')) {
                handleOAuthCallback();
            }
        });
        
        // Handle OAuth callback (simplified - would need backend implementation)
        function handleOAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const storedState = localStorage.getItem('linkedin_oauth_state');
            
            if (code && state && state === storedState) {
                showStatus('success', 'OAuth authorization received! Please exchange code for token on your backend.');
                
                // Clean up
                localStorage.removeItem('linkedin_oauth_state');
            }
        }
    </script>
</body>
</html>
