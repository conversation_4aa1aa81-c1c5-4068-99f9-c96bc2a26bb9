"""
Flask API Server for Social Media Messaging Integrations
Provides REST endpoints for all platform messaging capabilities
"""
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sys
import json
import logging
import time
from datetime import datetime

# Add integrations to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'integrations'))

from integrations.unified_messaging import UnifiedMessaging
from integrations.whatsapp_integration import WhatsAppMessaging
from integrations.telegram_integration import TelegramMessaging
from integrations.facebook_integration import FacebookMessaging
from integrations.instagram_integration import InstagramMessaging
from integrations.tiktok_integration import TikTokMessaging
from integrations.linkedin_integration import LinkedInMessaging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize unified messaging system
unified_messaging = UnifiedMessaging()

# Store individual platform instances for direct access
platforms = {
    'whatsapp': unified_messaging.whatsapp,
    'telegram': unified_messaging.telegram,
    'facebook': unified_messaging.facebook,
    'instagram': unified_messaging.instagram,
    'tiktok': unified_messaging.tiktok,
    'linkedin': unified_messaging.linkedin
}


@app.route('/')
def index():
    """API documentation endpoint"""
    return jsonify({
        "message": "Social Media Messaging API",
        "version": "1.0.0",
        "endpoints": {
            "messaging": {
                "send": "POST /api/messaging/send",
                "bulk": "POST /api/messaging/bulk",
                "status": "GET /api/messaging/status/<platform>"
            },
            "platforms": {
                "whatsapp": "/api/whatsapp/*",
                "telegram": "/api/telegram/*",
                "facebook": "/api/facebook/*",
                "instagram": "/api/instagram/*",
                "tiktok": "/api/tiktok/*",
                "linkedin": "/api/linkedin/*"
            },
            "auth_pages": {
                "whatsapp": "/auth/whatsapp",
                "telegram": "/auth/telegram",
                "facebook": "/auth/facebook",
                "instagram": "/auth/instagram",
                "tiktok": "/auth/tiktok",
                "linkedin": "/auth/linkedin"
            }
        },
        "supported_platforms": unified_messaging.get_supported_platforms()
    })


# ============================================================================
# UNIFIED MESSAGING ENDPOINTS
# ============================================================================

@app.route('/api/messaging/send', methods=['POST'])
def send_message():
    """
    Send single message
    Body: {
        "platform": "whatsapp",
        "recipient": "+**********",
        "message": "Hello!"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400
        
        platform = data.get('platform')
        recipient = data.get('recipient')
        message = data.get('message')
        
        if not all([platform, recipient, message]):
            return jsonify({
                "success": False,
                "message": "Missing required fields: platform, recipient, message"
            }), 400
        
        # Send message via unified system
        result = unified_messaging.send_message_by_platform(
            platform=platform,
            recipient=recipient,
            message=message,
            **data.get('options', {})
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in send_message: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}"
        }), 500


@app.route('/api/messaging/bulk', methods=['POST'])
def send_bulk_messages():
    """
    Send bulk messages
    Body: {
        "campaign": {
            "whatsapp": [
                {"phone": "+**********", "message": "Hello!", "name": "John"},
                {"phone": "+0987654321", "message": "Hi there!", "name": "Jane"}
            ],
            "telegram": [
                {"user_id": "@username", "message": "Hey!", "name": "Bob"}
            ]
        },
        "default_message": "Default message if not specified per recipient"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400
        
        campaign_data = data.get('campaign')
        default_message = data.get('default_message')
        
        if not campaign_data:
            return jsonify({
                "success": False,
                "message": "Missing campaign data"
            }), 400
        
        # Send bulk campaign
        result = unified_messaging.send_bulk_campaign(
            campaign_data=campaign_data,
            default_message=default_message
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in send_bulk_messages: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}"
        }), 500


@app.route('/api/messaging/status/<platform>', methods=['GET'])
def get_platform_status(platform):
    """Check if platform is connected and ready"""
    try:
        result = unified_messaging.get_platform_status(platform)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in get_platform_status: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}"
        }), 500


@app.route('/api/messaging/status', methods=['GET'])
def get_all_platform_status():
    """Check status of all platforms"""
    try:
        result = unified_messaging.get_platform_status()
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in get_all_platform_status: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}"
        }), 500


@app.route('/api/messaging/platforms', methods=['GET'])
def get_supported_platforms():
    """Get list of supported platforms and their capabilities"""
    try:
        platforms_list = unified_messaging.get_supported_platforms()
        capabilities = {}
        
        for platform in platforms_list:
            cap_result = unified_messaging.get_platform_capabilities(platform)
            if cap_result.get("success"):
                capabilities[platform] = cap_result["capabilities"]
        
        return jsonify({
            "success": True,
            "platforms": platforms_list,
            "capabilities": capabilities
        })
        
    except Exception as e:
        logger.error(f"Error in get_supported_platforms: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}"
        }), 500


# ============================================================================
# PLATFORM-SPECIFIC ENDPOINTS
# ============================================================================

# WhatsApp Endpoints
@app.route('/api/whatsapp/authenticate', methods=['POST'])
def whatsapp_authenticate():
    """Generate WhatsApp QR code for authentication"""
    try:
        result = platforms['whatsapp'].authenticate_account()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/whatsapp/status', methods=['GET'])
def whatsapp_status():
    """Check WhatsApp authentication status"""
    try:
        result = platforms['whatsapp'].check_authentication_status()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/whatsapp/test-connection', methods=['POST'])
def whatsapp_test_connection():
    """Test endpoint to simulate successful WhatsApp connection"""
    try:
        # This is for testing purposes only - simulates a successful scan
        # In real usage, this would be triggered by the Unipile API when QR is scanned

        # Get current account ID
        wa = platforms['whatsapp']
        if not wa.account_id:
            return jsonify({
                "success": False,
                "message": "No active QR code session. Generate QR code first."
            }), 400

        # Simulate successful connection
        wa.is_authenticated = True
        wa.config["last_authenticated"] = time.strftime("%Y-%m-%d %H:%M:%S")
        wa.config["phone_number"] = "+**********"  # Simulated phone number
        wa.save_config()

        return jsonify({
            "success": True,
            "message": "Connection simulated successfully",
            "account_id": wa.account_id,
            "note": "This is a test endpoint. In real usage, connection happens when QR code is scanned."
        })

    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


# Telegram Endpoints
@app.route('/api/telegram/setup', methods=['POST'])
def telegram_setup():
    """Setup Telegram bot with token"""
    try:
        data = request.get_json()
        bot_token = data.get('bot_token')
        
        if not bot_token:
            return jsonify({"success": False, "message": "Bot token required"}), 400
        
        result = platforms['telegram'].setup_bot(bot_token)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/telegram/test', methods=['GET'])
def telegram_test():
    """Test Telegram bot connection"""
    try:
        result = platforms['telegram'].get_bot_info()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


# Facebook Endpoints
@app.route('/api/facebook/setup', methods=['POST'])
def facebook_setup():
    """Setup Facebook page access with token"""
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        
        if not access_token:
            return jsonify({"success": False, "message": "Access token required"}), 400
        
        result = platforms['facebook'].setup_page_access(access_token)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/facebook/authenticate', methods=['POST'])
def facebook_authenticate():
    """Authenticate Facebook via Unipile"""
    try:
        result = platforms['facebook'].authenticate_account()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/facebook/test', methods=['GET'])
def facebook_test():
    """Test Facebook connection"""
    try:
        result = platforms['facebook'].get_page_info()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


# Instagram Endpoints
@app.route('/api/instagram/setup', methods=['POST'])
def instagram_setup():
    """Setup Instagram Business account"""
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        
        if not access_token:
            return jsonify({"success": False, "message": "Access token required"}), 400
        
        result = platforms['instagram'].setup_business_account(access_token)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/instagram/authenticate', methods=['POST'])
def instagram_authenticate():
    """Authenticate Instagram via Unipile"""
    try:
        result = platforms['instagram'].authenticate_account()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/instagram/test', methods=['GET'])
def instagram_test():
    """Test Instagram connection"""
    try:
        result = platforms['instagram'].get_account_info()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


# TikTok Endpoints
@app.route('/api/tiktok/setup', methods=['POST'])
def tiktok_setup():
    """Setup TikTok Business account"""
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        app_id = data.get('app_id')
        app_secret = data.get('app_secret')
        
        if not access_token:
            return jsonify({"success": False, "message": "Access token required"}), 400
        
        result = platforms['tiktok'].setup_business_account(access_token, app_id, app_secret)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/tiktok/authenticate', methods=['POST'])
def tiktok_authenticate():
    """Authenticate TikTok via Unipile"""
    try:
        result = platforms['tiktok'].authenticate_account()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


# LinkedIn Endpoints
@app.route('/api/linkedin/setup', methods=['POST'])
def linkedin_setup():
    """Setup LinkedIn API access"""
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        
        if not access_token:
            return jsonify({"success": False, "message": "Access token required"}), 400
        
        result = platforms['linkedin'].setup_api_access(access_token)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route('/api/linkedin/authenticate', methods=['POST'])
def linkedin_authenticate():
    """Authenticate LinkedIn via Unipile"""
    try:
        result = platforms['linkedin'].authenticate_account()
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


# ============================================================================
# AUTHENTICATION PAGES
# ============================================================================

@app.route('/auth/<platform>')
def serve_auth_page(platform):
    """Serve authentication HTML pages"""
    auth_files = {
        'whatsapp': 'integrations/whatsapp_integration/whatsapp_auth.html',
        'telegram': 'integrations/telegram_integration/telegram_auth.html',
        'facebook': 'integrations/facebook_integration/facebook_auth.html',
        'instagram': 'integrations/instagram_integration/instagram_auth.html',
        'tiktok': 'integrations/tiktok_integration/tiktok_auth.html',
        'linkedin': 'integrations/linkedin_integration/linkedin_auth.html'
    }
    
    if platform in auth_files:
        file_path = auth_files[platform]
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            return f"Authentication page for {platform} not found", 404
    else:
        return f"Platform {platform} not supported", 404


# ============================================================================
# ERROR HANDLERS
# ============================================================================

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "message": "Endpoint not found",
        "available_endpoints": "/api/messaging/*, /api/<platform>/*, /auth/<platform>"
    }), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "message": "Internal server error"
    }), 500


if __name__ == '__main__':
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Setup file logging
    file_handler = logging.FileHandler('logs/api_server.log')
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    app.logger.addHandler(file_handler)
    
    print("🚀 Starting Social Media Messaging API Server...")
    print("📱 Available platforms: WhatsApp, Telegram, Facebook, Instagram, TikTok, LinkedIn")
    print("🌐 API Documentation: http://localhost:5000/")
    print("🔐 Authentication pages: http://localhost:5000/auth/<platform>")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
